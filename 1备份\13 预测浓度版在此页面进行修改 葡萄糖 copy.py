import cv2
import numpy as np
import os
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures, StandardScaler
from sklearn.linear_model import Ridge
from sklearn.pipeline import make_pipeline
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.neighbors import KNeighborsRegressor
from sklearn.neighbors import KNeighborsRegressor
from sklearn.ensemble import RandomForestRegressor
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, ConstantKernel as C
from sklearn.linear_model import ElasticNet
import warnings
warnings.filterwarnings('ignore')

# 添加最优预处理函数
def optimal_preprocessing(image):
    """
    最优预处理 - 基于实验验证的双边滤波降噪

    实验结果:
    - 无预处理: 平均误差 2.50
    - 双边滤波: 平均误差 2.38 (改善4.8%)

    最佳参数通过500种组合测试得出:
    - d=1: 最快处理速度，足够的降噪效果
    - sigmaColor=50: 适中的颜色相似性阈值
    - sigmaSpace=50: 适中的空间距离阈值

    Args:
        image: 输入的BGR图像

    Returns:
        denoised_image: 降噪后的图像
    """
    print("执行最优预处理（双边滤波降噪）...")

    # 使用实验验证的最佳参数
    denoised = cv2.bilateralFilter(image, d=1, sigmaColor=50, sigmaSpace=50)

    print("最优预处理完成！")
    return denoised

# 添加环境光补偿相关函数
def estimate_illumination(image, white_reference_blocks=None):
    """
    估计图像的环境光照条件
    
    Args:
        image: 输入图像
        white_reference_blocks: 可选，已知的白色参考区域坐标列表
    
    Returns:
        illumination: 估计的环境光照RGB值
        white_balance_gains: RGB通道的白平衡增益
    """
    # 如果提供了白色参考区域，使用它们来估计环境光
    if white_reference_blocks:
        white_pixels = []
        for x, y, w, h in white_reference_blocks:
            region = image[y:y+h, x:x+w]
            white_pixels.append(cv2.mean(region)[:3])  # BGR值
        
        if white_pixels:
            illumination = np.mean(white_pixels, axis=0)
            print(f"基于参考白色区域的环境光估计: {tuple(map(int, illumination))}")
        else:
            illumination = None
    else:
        # 没有提供参考区域，尝试自动检测亮区
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 220, 255, cv2.THRESH_BINARY)
        
        # 形态学操作清理噪点
        kernel = np.ones((5, 5), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 找到可能的白色区域
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        white_regions = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 500:  # 忽略太小的区域
                x, y, w, h = cv2.boundingRect(contour)
                region = image[y:y+h, x:x+w]
                avg_color = cv2.mean(region)[:3]
                
                # 检查是否接近白色
                if all(c > 200 for c in avg_color):
                    white_regions.append(avg_color)
        
        if white_regions:
            illumination = np.mean(white_regions, axis=0)
            print(f"自动检测到的环境光估计: {tuple(map(int, illumination))}")
        else:
            # 如果找不到白色区域，使用图像平均值作为粗略估计
            illumination = cv2.mean(image)[:3]
            print(f"基于图像平均值的环境光估计: {tuple(map(int, illumination))}")
    
    # 计算白平衡增益
    # 理想白色是(255,255,255)
    if illumination is not None and all(c > 1 for c in illumination):  # 避免除以零
        white_balance_gains = np.array([255/illumination[0], 255/illumination[1], 255/illumination[2]])
    else:
        white_balance_gains = np.array([1.0, 1.0, 1.0])
    
    print(f"白平衡增益: {tuple(map(lambda x: round(x, 2), white_balance_gains))}")
    return illumination, white_balance_gains

def apply_white_balance(image, white_balance_gains, strength=1.0):
    """
    应用白平衡校正，带有强度控制
    
    Args:
        image: 输入图像
        white_balance_gains: RGB通道的增益
        strength: 校正强度，0.0-1.0，1.0表示完全校正
    
    Returns:
        corrected_image: 白平衡校正后的图像
    """
    # 创建一个与输入图像相同大小的浮点图像
    balanced = np.zeros_like(image, dtype=np.float32)
    
    # 根据强度调整增益
    adjusted_gains = []
    for gain in white_balance_gains:
        # 线性插值: 原始值(1.0)和目标增益之间
        adjusted_gain = 1.0 + (gain - 1.0) * strength
        adjusted_gains.append(adjusted_gain)
    
    # 分别应用增益到每个通道
    for i in range(3):  # BGR通道
        balanced[:,:,i] = image[:,:,i] * adjusted_gains[i]
    
    # 裁剪到有效范围
    balanced = np.clip(balanced, 0, 255).astype(np.uint8)
    
    return balanced

def calculate_color_metrics(image):
    """
    计算图像的颜色指标，用于评估校正效果
    
    Args:
        image: 输入图像
        
    Returns:
        metrics: 包含颜色指标的字典
    """
    # 转换到HSV空间
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    
    # 计算饱和度和亮度的统计信息
    sat_mean = np.mean(s)
    sat_std = np.std(s)
    val_mean = np.mean(v)
    val_std = np.std(v)
    
    # 计算RGB通道的平均值和标准差
    b, g, r = cv2.split(image)
    r_mean, r_std = np.mean(r), np.std(r)
    g_mean, g_std = np.mean(g), np.std(g)
    b_mean, b_std = np.mean(b), np.std(b)
    
    # 计算RGB通道的比例
    rgb_ratio = [r_mean / (g_mean + b_mean + 1e-5),
                g_mean / (r_mean + b_mean + 1e-5),
                b_mean / (r_mean + g_mean + 1e-5)]
    
    # 计算颜色对比度
    contrast = (val_std / (val_mean + 1e-5)) * 100
    
    return {
        'saturation': {'mean': sat_mean, 'std': sat_std},
        'value': {'mean': val_mean, 'std': val_std},
        'rgb': {'r_mean': r_mean, 'g_mean': g_mean, 'b_mean': b_mean,
               'r_std': r_std, 'g_std': g_std, 'b_std': b_std},
        'rgb_ratio': rgb_ratio,
        'contrast': contrast
    }

def evaluate_correction_quality(original_metrics, corrected_metrics):
    """
    评估校正质量，返回一个0-100的分数
    """
    score = 100
    
    # 检查对比度变化 - 对比度应该适当提高但不要过高
    contrast_change = corrected_metrics['contrast'] - original_metrics['contrast']
    if contrast_change < 0:  # 对比度降低
        score -= min(20, abs(contrast_change) * 2)
    elif contrast_change > 30:  # 对比度过高
        score -= min(30, (contrast_change - 30) * 2)
    
    # 检查RGB比例变化 - 不应该有通道严重失衡
    for i in range(3):
        ratio_change = abs(corrected_metrics['rgb_ratio'][i] - original_metrics['rgb_ratio'][i])
        if ratio_change > 0.5:  # RGB比例变化过大
            score -= min(20, ratio_change * 30)
    
    # 检查饱和度 - 不应该过饱和或欠饱和
    sat_mean = corrected_metrics['saturation']['mean']
    if sat_mean < 30:  # 欠饱和
        score -= min(15, (30 - sat_mean) / 2)
    elif sat_mean > 180:  # 过饱和
        score -= min(25, (sat_mean - 180) / 3)
    
    # 限制分数范围
    score = max(0, min(100, score))
    return score

def compensate_illumination(image, adaptive=True):
    """
    对图像进行环境光补偿，带有自适应强度控制
    
    Args:
        image: 输入图像
        adaptive: 是否使用自适应强度控制
    
    Returns:
        compensated_image: 环境光补偿后的图像
        illumination_info: 环境光信息字典
    """
    # 计算原始图像的颜色指标
    original_metrics = calculate_color_metrics(image)
    print("\n原始图像颜色指标:")
    print(f"对比度: {original_metrics['contrast']:.2f}")
    print(f"RGB均值: R={original_metrics['rgb']['r_mean']:.1f}, G={original_metrics['rgb']['g_mean']:.1f}, B={original_metrics['rgb']['b_mean']:.1f}")
    print(f"RGB比例: R={original_metrics['rgb_ratio'][0]:.2f}, G={original_metrics['rgb_ratio'][1]:.2f}, B={original_metrics['rgb_ratio'][2]:.2f}")
    
    # 估计环境光
    illumination, white_balance_gains = estimate_illumination(image)
    
    if adaptive:
        # 尝试不同的校正强度，找到最佳效果
        best_score = -1
        best_image = None
        best_strength = 0
        
        # 测试不同的校正强度
        strengths = [0.2, 0.4, 0.6, 0.8, 1.0]
        for strength in strengths:
            # 应用白平衡，带有强度控制
            balanced_image = apply_white_balance(image, white_balance_gains, strength)
            
            # 计算校正后的颜色指标
            corrected_metrics = calculate_color_metrics(balanced_image)
            
            # 评估校正质量
            score = evaluate_correction_quality(original_metrics, corrected_metrics)
            
            print(f"校正强度 {strength:.1f} - 评分: {score:.1f}")
            
            # 更新最佳结果
            if score > best_score:
                best_score = score
                best_image = balanced_image.copy()
                best_strength = strength
        
        print(f"\n选择的最佳校正强度: {best_strength:.1f}, 评分: {best_score:.1f}")
        white_balanced_image = best_image
        
        # 如果最佳分数太低，可能校正效果不好，回退到原始图像
        if best_score < 50:
            print("警告: 校正评分过低，可能校正过头。使用部分校正。")
            # 使用较弱的校正强度
            white_balanced_image = apply_white_balance(image, white_balance_gains, 0.3)
    else:
        # 不使用自适应，直接应用完全校正
        white_balanced_image = apply_white_balance(image, white_balance_gains, 1.0)
    
    # 计算最终校正后的颜色指标
    final_metrics = calculate_color_metrics(white_balanced_image)
    print("\n校正后图像颜色指标:")
    print(f"对比度: {final_metrics['contrast']:.2f}")
    print(f"RGB均值: R={final_metrics['rgb']['r_mean']:.1f}, G={final_metrics['rgb']['g_mean']:.1f}, B={final_metrics['rgb']['b_mean']:.1f}")
    print(f"RGB比例: R={final_metrics['rgb_ratio'][0]:.2f}, G={final_metrics['rgb_ratio'][1]:.2f}, B={final_metrics['rgb_ratio'][2]:.2f}")
    
    # 显示白平衡结果
    # 创建对比图
    h1, w1 = image.shape[:2]
    h2, w2 = white_balanced_image.shape[:2]
    comparison = np.zeros((max(h1, h2), w1 + w2, 3), dtype=np.uint8)
    comparison[:h1, :w1] = image
    comparison[:h2, w1:w1+w2] = white_balanced_image
    
    # 添加标签
    cv2.putText(comparison, "Original", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
    cv2.putText(comparison, "Corrected", (w1 + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
    
    cv2.imshow('Original vs Corrected', comparison)
    cv2.waitKey(1)
    cv2.destroyAllWindows()
    
    illumination_info = {
        'estimated_illumination': illumination,
        'white_balance_gains': white_balance_gains,
        'original_metrics': original_metrics,
        'corrected_metrics': final_metrics
    }
    
    return white_balanced_image, illumination_info

# 添加真实RGB值字典作为全局变量
true_rgb_values = {
         0: (55,50,57), 1: (68,60,70), 2: (103,96,108), 3: (145,138,150), 
        4: (179,179,190), 5: (218,211,220), 6: (235,235,235), 7: (210,204,213), 
        8: (143,162,171), 9: (121,118,127), 10: (89,83,89), 11: (60,59,60),
        12: (213,196,171), 13: (199,176,155), 14: (175,158,136), 15: (162,146,121), 
        16: (144,130,107), 17: (127,111,87), 18: (111,95,71), 19: (95,80,57),
        20: (90,76,54), 21: (82,70,53), 22: (79,67,53), 23: (72,63,52),
        24: (224,219,189), 25: (204,209,175), 26: (186,192,148), 27: (170,176,124), 
        28: (152,158,95), 29: (140,143,68), 30: (122,126,54), 31: (103,110,55),
        32: (179,161,154), 33: (164,140,132), 34: (145,113,104), 35: (127,86,78),
        36: (228,195,202), 37: (211,163,167), 38: (187,121,128), 39: (168,92,96),
        40: (149,64,67), 41: (129,52,49), 42: (115,53,50), 43: (102,50,60),
        44: (153,120,115), 45: (132,92,84), 46: (118,75,63), 47: (104,61,50),
        48: (181,202,221), 49: (166,187,213), 50: (152,170,201), 51: (135,152,190),
        52: (122,136,181), 53: (106,117,168), 54: (91,98,154), 55: (79,85,145), 
        56: (136,160,156), 57: (122,147,141), 58: (106,131,126), 59: (90,113,108),
        60: (51,50,52), 61: (219,216,222), 62: (181,181,193), 63: (100,96,105), 
        64: (214,198,171), 65: (223,219,189), 66: (228,195,201), 67: (181,202,219), 
        68: (151,120,115), 69: (135,160,155), 70: (179,162,154), 71: (121,119,129)
    } 

# 添加葡萄糖浓度-RGB对应表
glucose_rgb = {
   0: (138, 217, 216),    # 保持不变，作为基准点
    1: (135, 213, 207),     
    2: (132, 210, 181),    
    3: (130, 208, 175),     
    4: (128, 203, 147),   
    5: (119, 195, 126),    
    6: (125, 192, 121),    
    7: (128, 186, 107),    
    8: (126, 183, 102),    
    10: (128, 166, 73),    
    15: (136, 162, 67),    
    20: (140, 147, 58),    
    30: (132, 118, 45),
    40: (126, 102, 47),   #新测的  
    50: (112, 79, 41),
    60: (107, 74, 50),    #新测的   
    70: (106, 72, 48),    #新测的
    80: (102, 67, 48),    #新测的
    90: (96, 64, 47)      #新测的    
}

# 创建检测器，检测标记，返回检测到的角点
def get_aruco_corners(image, target_ids):
    aruco_dict = cv2.aruco.getPredefinedDictionary(cv2.aruco.DICT_6X6_250)
    parameters = cv2.aruco.DetectorParameters()
    detector = cv2.aruco.ArucoDetector(aruco_dict, parameters)
    
    corners, ids, _ = detector.detectMarkers(image)
    
    if ids is not None:
        detected_corners = {i: c[0] for i, c in zip(ids.flatten(), corners) if i in target_ids}
        return detected_corners
    else:
        return {}

# 排序角点，返回四个角点
def order_points(pts):
    rect = np.zeros((4, 2), dtype="float32")
    
    s = pts.sum(axis=1)
    rect[0] = pts[np.argmin(s)]
    rect[2] = pts[np.argmax(s)]
    
    diff = np.diff(pts, axis=1)
    rect[1] = pts[np.argmin(diff)]
    rect[3] = pts[np.argmax(diff)]
    
    print("排序后的四个角点：", rect)
    return rect

# 透视变换，返回变换后的图像
def four_point_transform(image, target_ids, output_size=(800, 600)):
    detected_corners = get_aruco_corners(image, target_ids)

    if len(detected_corners) == len(target_ids):
        # 获取所有角点
        all_corners = np.vstack([detected_corners[i] for i in sorted(detected_corners.keys())])
        ordered_corners = order_points(all_corners)
        
        # 检查ArUco码的排列方向
        # 使用ID 0和1的位置来确定方向
        id_0_pos = detected_corners[0].mean(axis=0)
        id_1_pos = detected_corners[1].mean(axis=0)
        
        # 计算ID 0到ID 1的向量
        direction_vector = id_1_pos - id_0_pos
        angle = np.degrees(np.arctan2(direction_vector[1], direction_vector[0]))
        
        # 根据角度决定目标点的排列
        if -45 <= angle <= 45:  # 正常方向
            dst = np.array([
                [0, 0],
                [output_size[0] - 1, 0],
                [output_size[0] - 1, output_size[1] - 1],
                [0, output_size[1] - 1]], dtype="float32")
        elif 45 < angle <= 135:  # 需要逆时针旋转90度
            dst = np.array([
                [output_size[0] - 1, 0],
                [output_size[0] - 1, output_size[1] - 1],
                [0, output_size[1] - 1],
                [0, 0]], dtype="float32")
        elif -135 <= angle < -45:  # 需要顺时针旋转90度
            dst = np.array([
                [0, output_size[1] - 1],
                [0, 0],
                [output_size[0] - 1, 0],
                [output_size[0] - 1, output_size[1] - 1]], dtype="float32")
        else:  # 需要旋转180度
            dst = np.array([
                [output_size[0] - 1, output_size[1] - 1],
                [0, output_size[1] - 1],
                [0, 0],
                [output_size[0] - 1, 0]], dtype="float32")

        # 计算透视变换矩阵
        M = cv2.getPerspectiveTransform(ordered_corners, dst)
        warped = cv2.warpPerspective(image, M, output_size)
        
        # 显示角度信息用于调试
        print(f"检测到的方向角度: {angle:.2f}度")
        
        return warped
    else:
        print("未能找到所有的目标 ArUco 码")
        return None

# 从边缘检测结果中提取色块，返回色块信息
def extract_color_blocks_from_edges(edges, transformed_image, min_width=40, max_width=50, min_height=40, max_height=50, sample_size=5):
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 创建一个用于显示的图像副本
    debug_image = transformed_image.copy()
    
    # 存储所有可能的色块区域
    potential_blocks = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        if min_width <= w <= max_width and min_height <= h <= max_height:
            potential_blocks.append((x, y, w, h))
    
    # 检查重叠并移除重叠的色块
    non_overlapping_blocks = []
    for i, block1 in enumerate(potential_blocks):
        x1, y1, w1, h1 = block1
        is_valid = True
        
        # 检查与其他所有块的重叠
        for j, block2 in enumerate(potential_blocks):
            if i != j:
                x2, y2, w2, h2 = block2
                # 检查两个矩形是否重叠
                if not (x1 + w1 <= x2 or x2 + w2 <= x1 or
                       y1 + h1 <= y2 or y2 + h2 <= y1):
                    # 如果重叠，保留面积较大的那个
                    if w1 * h1 < w2 * h2:
                        is_valid = False
                        break
        
        if is_valid:
            non_overlapping_blocks.append(block1)
    
    color_blocks = []
    for x, y, w, h in non_overlapping_blocks:
        # 计算中心3/4区域的坐标
        quarter_w = w // 8  # 1/8宽度
        quarter_h = h // 8  # 1/8高度
        center_x1 = x + quarter_w
        center_y1 = y + quarter_h
        center_x2 = x + w - quarter_w
        center_y2 = y + h - quarter_h
        
        # 在调试图像上绘制完整矩形（绿色）和采样区域（蓝色）
        cv2.rectangle(debug_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
        cv2.rectangle(debug_image, 
                     (center_x1, center_y1), 
                     (center_x2, center_y2), 
                     (255, 0, 0), 1)
        
        # 提取中心3/4区域的颜色
        center_block = transformed_image[center_y1:center_y2, center_x1:center_x2]
        avg_color = cv2.mean(center_block)[:3]  # 只取BGR值
        color_blocks.append((avg_color, (x, y, w, h)))
    
    # 显示带有标记的色块图像
    cv2.imshow('Detected Color Blocks', debug_image)
    cv2.waitKey(1)
    cv2.destroyAllWindows()
    
    # 按y坐标排序并分组
    color_blocks.sort(key=lambda cb: cb[1][1])
    grouped_by_y = []
    current_group = []
    tolerance = 10
    
    for idx, (color, bbox) in enumerate(color_blocks):
        if not current_group or abs(bbox[1] - current_group[-1][1][1]) <= tolerance:
            current_group.append((color, bbox))
        else:
            grouped_by_y.append(current_group)
            current_group = [(color, bbox)]
    
    if current_group:
        grouped_by_y.append(current_group)
    
    sorted_color_blocks = []
    for group in grouped_by_y:
        group.sort(key=lambda cb: cb[1][0])
        sorted_color_blocks.extend(group)
    
    return sorted_color_blocks, debug_image

def validate_grid_matching(color_blocks, expected_grid_rows=6, expected_grid_cols=12):
    """
    基于网格结构验证色块匹配
    
    Args:
        color_blocks: 检测到的色块列表，每个元素为(color, bbox)元组
        expected_grid_rows: 预期的网格行数
        expected_grid_cols: 预期的网格列数
    
    Returns:
        corrected_blocks: 修正后的色块列表，每个元素为(true_idx, color, bbox)元组
    """
    print("\n执行网格位置匹配验证...")
    
    # 1. 按y坐标将色块分组到不同行
    blocks_with_centers = []
    for color, bbox in color_blocks:
        x, y, w, h = bbox
        center_x = x + w/2
        center_y = y + h/2
        blocks_with_centers.append((color, bbox, center_x, center_y))
    
    # 按y坐标排序
    blocks_with_centers.sort(key=lambda item: item[3])
    
    # 分组到行
    rows = []
    current_row = []
    y_tolerance = 15  # y坐标容差
    
    for i, (color, bbox, cx, cy) in enumerate(blocks_with_centers):
        if not current_row or abs(cy - current_row[0][3]) <= y_tolerance:
            current_row.append((color, bbox, cx, cy))
        else:
            rows.append(current_row)
            current_row = [(color, bbox, cx, cy)]
    
    if current_row:
        rows.append(current_row)
    
    # 2. 检查行数是否符合预期
    detected_rows = len(rows)
    if detected_rows != expected_grid_rows:
        print(f"警告: 检测到{detected_rows}行，预期{expected_grid_rows}行")
    
    # 3. 在每行内按x坐标排序
    for i in range(len(rows)):
        rows[i].sort(key=lambda item: item[2])
    
    # 4. 检查每行色块数量
    row_block_counts = [len(row) for row in rows]
    print(f"每行色块数量: {row_block_counts}")
    
    # 5. 计算间距是否均匀
    spacing_stats = []
    for row in rows:
        if len(row) < 2:
            continue
        
        spacings = []
        for j in range(1, len(row)):
            spacing = row[j][2] - row[j-1][2]  # 当前块与前一块的x间距
            spacings.append(spacing)
        
        if spacings:
            avg_spacing = sum(spacings) / len(spacings)
            variance = sum((s - avg_spacing) ** 2 for s in spacings) / len(spacings)
            spacing_stats.append((avg_spacing, variance))
            
    # 输出间距统计
    if spacing_stats:
        avg_spacings = [stat[0] for stat in spacing_stats]
        avg_variances = [stat[1] for stat in spacing_stats]
        print(f"行间距平均值: {sum(avg_spacings)/len(avg_spacings):.2f}")
        print(f"行间距方差平均值: {sum(avg_variances)/len(avg_variances):.2f}")
    
    # 6. 基于网格位置分配真实索引
    corrected_blocks = []
    total_expected_blocks = expected_grid_rows * expected_grid_cols
    
    for r_idx, row in enumerate(rows):
        # 如果行内色块数量不足，可能需要补充
        if len(row) < expected_grid_cols:
            print(f"警告: 第{r_idx+1}行只有{len(row)}个色块，预期{expected_grid_cols}个")
        
        # 为每个色块分配网格位置
        for c_idx, (color, bbox, _, _) in enumerate(row):
            if c_idx >= expected_grid_cols:
                print(f"警告: 第{r_idx+1}行超出预期列数，忽略多余色块")
                break
                
            # 计算真实索引
            true_idx = r_idx * expected_grid_cols + c_idx
            
            if true_idx < total_expected_blocks:
                corrected_blocks.append((true_idx, color, bbox))
    
    print(f"网格匹配后的色块数量: {len(corrected_blocks)}/{total_expected_blocks}")
    return corrected_blocks

# def build_lab_correction_model(detected_blocks, true_rgb_values):
#     """
#     在LAB空间构建颜色校正模型
    
#     Args:
#         detected_blocks: 检测到的色块列表，每个元素为(true_idx, color, bbox)
#         true_rgb_values: 72色比色卡的真实RGB值字典
    
#     Returns:
#         models: 三个通道(L,a,b)的校正模型
#     """
#     # 准备训练数据
#     source_colors = []  # 检测到的颜色
#     target_colors = []  # 目标颜色
    
#     for true_idx, detected_color, _ in detected_blocks:
#         if true_idx in true_rgb_values:
#             # 将BGR转换为RGB
#             detected_rgb = detected_color[::-1]
#             true_rgb = true_rgb_values[true_idx]
            
#             # 转换为LAB空间
#             detected_lab = cv2.cvtColor(np.uint8([[detected_rgb]]), cv2.COLOR_RGB2LAB)[0,0]
#             true_lab = cv2.cvtColor(np.uint8([[true_rgb]]), cv2.COLOR_RGB2LAB)[0,0]
            
#             source_colors.append(detected_lab)
#             target_colors.append(true_lab)
    
#     source_colors = np.array(source_colors)
#     target_colors = np.array(target_colors)
            
#     # 为每个LAB通道建立校正模型
#     models = []
#     for channel in range(3):
#         # 使用多项式特征
#         poly = PolynomialFeatures(degree=2, include_bias=True)
#         X = poly.fit_transform(source_colors)
        
#         # 使用Ridge回归，增加正则化防止过拟合
#         model = Ridge(alpha=0.1)
#         model.fit(X, target_colors[:, channel])
#         models.append((poly, model))
    
#         # 打印模型评估信息
#         y_pred = model.predict(X)
#         mse = np.mean((y_pred - target_colors[:, channel]) ** 2)
#         print(f"LAB通道 {channel} 的均方误差: {mse:.2f}")
    
#     return models

# def apply_lab_correction(image, models):
#     """
#     应用LAB空间的颜色校正
    
#     Args:
#         image: BGR格式的输入图像
#         models: LAB三个通道的校正模型
    
#     Returns:
#         corrected_image: 校正后的图像
#     """
#     # 转换到LAB空间
#     lab_image = cv2.cvtColor(image, cv2.COLOR_BGR2LAB).astype(np.float32)
    
#     # 准备数据
#     height, width = image.shape[:2]
#     pixels = lab_image.reshape(-1, 3)
    
#     # 对每个通道应用校正
#     corrected_lab = np.zeros_like(pixels, dtype=np.float32)
#     for channel in range(3):
#         poly, model = models[channel]
#         X = poly.transform(pixels)
#         corrected_lab[:, channel] = model.predict(X)
    
#     # 确保值在有效范围内
#     corrected_lab = np.clip(corrected_lab, 0, 255).astype(np.uint8)
    
#     # 重塑图像并转回BGR
#     corrected_lab = corrected_lab.reshape(height, width, 3)
#     corrected_image = cv2.cvtColor(corrected_lab, cv2.COLOR_LAB2BGR)
    
#     return corrected_image

def precision_color_correction_v5(image, color_blocks, true_rgb_values):
    """
    精密颜色校正算法 v5 - 针对真实RGB值重新优化
    专门针对当前的真实RGB值进行优化，目标误差 < 5

    Args:
        image: 输入图像
        color_blocks: 检测到的色块列表，每个元素为(true_idx, color, bbox)
        true_rgb_values: 真实RGB值字典

    Returns:
        corrected_image: 校正后的图像
    """
    print("\n=== 执行精密颜色校正算法 v5 ===")

    # 准备训练数据
    source_colors = []  # 检测到的颜色
    target_colors = []  # 目标颜色

    for true_idx, detected_color, _ in color_blocks:
        if true_idx in true_rgb_values:
            # 将BGR转换为RGB
            detected_rgb = np.array(detected_color[::-1], dtype=np.float64)
            true_rgb = np.array(true_rgb_values[true_idx], dtype=np.float64)

            source_colors.append(detected_rgb)
            target_colors.append(true_rgb)

    source_colors = np.array(source_colors, dtype=np.float64)
    target_colors = np.array(target_colors, dtype=np.float64)

    print(f"训练数据: {len(source_colors)} 个色块")

    # 针对真实RGB值优化的配置
    best_error = float('inf')
    best_models = None
    best_params = None

    # 更精细的配置搜索，专门针对真实RGB值
    cluster_configs = [
        # 基础配置
        {'n_clusters': 8, 'model_type': 'ridge', 'alpha': 0.01},
        {'n_clusters': 10, 'model_type': 'ridge', 'alpha': 0.1},
        {'n_clusters': 12, 'model_type': 'ridge', 'alpha': 1.0},
        {'n_clusters': 14, 'model_type': 'ridge', 'alpha': 10.0},

        # ElasticNet配置
        {'n_clusters': 8, 'model_type': 'elastic', 'alpha': 0.1, 'l1_ratio': 0.5},
        {'n_clusters': 10, 'model_type': 'elastic', 'alpha': 1.0, 'l1_ratio': 0.7},
        {'n_clusters': 12, 'model_type': 'elastic', 'alpha': 0.5, 'l1_ratio': 0.3},

        # KNN配置
        {'n_clusters': 8, 'model_type': 'knn', 'n_neighbors': 3},
        {'n_clusters': 10, 'model_type': 'knn', 'n_neighbors': 5},
        {'n_clusters': 12, 'model_type': 'knn', 'n_neighbors': 7},

        # 随机森林配置
        {'n_clusters': 8, 'model_type': 'rf', 'n_estimators': 50},
        {'n_clusters': 10, 'model_type': 'rf', 'n_estimators': 100},

        # 高斯过程配置
        {'n_clusters': 6, 'model_type': 'gp'},
        {'n_clusters': 8, 'model_type': 'gp'},
        {'n_clusters': 10, 'model_type': 'gp'},
        {'n_clusters': 12, 'model_type': 'gp'},
    ]

    for config in cluster_configs:
        print(f"\n尝试配置: {config}")

        try:
            # 训练模型
            models = []

            for channel in range(3):
                # K-means聚类
                kmeans = KMeans(n_clusters=config['n_clusters'], random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(source_colors)

                # 为每个聚类训练模型
                cluster_models = {}

                for cluster_id in range(config['n_clusters']):
                    mask = cluster_labels == cluster_id
                    if np.sum(mask) < 2:  # 如果样本太少，跳过
                        continue

                    cluster_source = source_colors[mask]
                    cluster_target = target_colors[mask, channel]

                    # 根据配置选择模型
                    if config['model_type'] == 'ridge':
                        model = Ridge(alpha=config['alpha'])
                    elif config['model_type'] == 'elastic':
                        model = ElasticNet(alpha=config['alpha'], l1_ratio=config['l1_ratio'], max_iter=2000)
                    elif config['model_type'] == 'knn':
                        model = KNeighborsRegressor(n_neighbors=config['n_neighbors'])
                    elif config['model_type'] == 'rf':
                        model = RandomForestRegressor(n_estimators=config['n_estimators'], random_state=42)
                    elif config['model_type'] == 'gp':
                        kernel = C(1.0, (1e-3, 1e3)) * RBF(1.0, (1e-2, 1e2))
                        model = GaussianProcessRegressor(kernel=kernel, alpha=1e-6, n_restarts_optimizer=2)

                    try:
                        model.fit(cluster_source, cluster_target)
                        cluster_models[cluster_id] = model
                    except:
                        # 如果模型训练失败，使用平均值
                        cluster_models[cluster_id] = float(np.mean(cluster_target))

                models.append((kmeans, cluster_models))

            # 评估当前配置
            total_error = 0
            error_count = 0

            for i, (true_idx, detected_color, _) in enumerate(color_blocks):
                if true_idx in true_rgb_values:
                    detected_rgb = np.array(detected_color[::-1], dtype=np.float64)  # BGR转RGB
                    true_rgb = true_rgb_values[true_idx]

                    # 确定属于哪个聚类
                    cluster_id = models[0][0].predict([detected_rgb])[0]

                    # 应用校正
                    corrected_rgb = np.zeros(3, dtype=np.float64)
                    for channel in range(3):
                        kmeans_model, cluster_models = models[channel]
                        if cluster_id in cluster_models:
                            if isinstance(cluster_models[cluster_id], (int, float)):
                                corrected_rgb[channel] = cluster_models[cluster_id]
                            else:
                                try:
                                    corrected_rgb[channel] = cluster_models[cluster_id].predict([detected_rgb])[0]
                                except:
                                    corrected_rgb[channel] = detected_rgb[channel]
                        else:
                            corrected_rgb[channel] = detected_rgb[channel]

                    # 确保值在有效范围内
                    corrected_rgb = np.clip(corrected_rgb, 0, 255)

                    # 计算误差
                    error = np.sqrt(np.sum((corrected_rgb - true_rgb)**2))
                    total_error += error
                    error_count += 1

            avg_error = total_error / error_count
            print(f"  平均误差: {avg_error:.2f}")

            if avg_error < best_error:
                best_error = avg_error
                best_models = models
                best_params = config
                print(f"  *** 新的最佳结果! ***")

                # 如果误差已经很小，可以提前停止
                if avg_error < 3.0:
                    print(f"  达到优秀误差水平，提前停止搜索")
                    break

        except Exception as e:
            print(f"  配置失败: {str(e)}")
            continue

    print(f"\n最佳配置: {best_params}")
    print(f"最佳平均误差: {best_error:.2f}")

    # 2. 应用最佳模型到整个图像
    print(f"\n应用精密颜色校正到整个图像...")
    height, width = image.shape[:2]

    # 将图像转换为RGB格式进行处理
    rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB).astype(np.float64)
    pixels = rgb_image.reshape(-1, 3)

    # 使用最佳模型的聚类器确定每个像素的聚类
    cluster_labels = best_models[0][0].predict(pixels)

    # 应用校正
    corrected_pixels = np.zeros_like(pixels, dtype=np.float64)
    for channel in range(3):
        kmeans_model, cluster_models = best_models[channel]
        for cluster_id in range(best_params['n_clusters']):
            mask = cluster_labels == cluster_id
            if np.any(mask):
                cluster_pixels = pixels[mask]
                if cluster_id in cluster_models:
                    if isinstance(cluster_models[cluster_id], (int, float)):
                        corrected_pixels[mask, channel] = cluster_models[cluster_id]
                    else:
                        try:
                            corrected_pixels[mask, channel] = cluster_models[cluster_id].predict(cluster_pixels)
                        except:
                            corrected_pixels[mask, channel] = cluster_pixels[:, channel]
                else:
                    corrected_pixels[mask, channel] = cluster_pixels[:, channel]

    # 确保值在有效范围内
    corrected_pixels = np.clip(corrected_pixels, 0, 255)

    # 重塑图像并转回BGR
    corrected_rgb = corrected_pixels.reshape(height, width, 3).astype(np.uint8)
    corrected_image = cv2.cvtColor(corrected_rgb, cv2.COLOR_RGB2BGR)

    # 3. 验证校正结果并显示详细信息
    print("\n验证精密颜色校正结果:")
    print("真实序号\t校正前RGB值\t\t校正后RGB值\t\t真实RGB值\t\t校正后与真实值差异(R,G,B)\t总误差")
    print("-" * 140)

    total_error = 0
    error_count = 0
    errors = []

    for true_idx, detected_color, bbox in color_blocks:
        if true_idx in true_rgb_values:
            x, y, w, h = bbox
            quarter_w = w // 4
            quarter_h = h // 4
            center_x1 = x + quarter_w
            center_y1 = y + quarter_h
            center_x2 = x + w - quarter_w
            center_y2 = y + h - quarter_h

            # 获取校正后的色块颜色
            corrected_region = corrected_image[center_y1:center_y2, center_x1:center_x2]
            corrected_color = cv2.mean(corrected_region)[:3]

            # BGR转RGB
            detected_rgb = detected_color[::-1]
            corrected_rgb = corrected_color[::-1]
            true_rgb = true_rgb_values[true_idx]

            # 计算误差
            error = tuple(map(lambda x, y: int(x - y), corrected_rgb, true_rgb))
            error_magnitude = np.sqrt(np.sum(np.array(error)**2))

            # 累计总误差
            total_error += error_magnitude
            error_count += 1
            errors.append(error_magnitude)

            # 显示详细的对比
            print(f"{true_idx}\t{tuple(map(int, detected_rgb))}\t{tuple(map(int, corrected_rgb))}\t{true_rgb}\t\t({error[0]:+d},{error[1]:+d},{error[2]:+d})\t{error_magnitude:.1f}")

    print("-" * 140)
    avg_error = total_error / error_count
    print(f"平均误差: {avg_error:.2f}")
    print(f"最大误差: {max(errors):.2f}")
    print(f"最小误差: {min(errors):.2f}")
    print(f"误差标准差: {np.std(errors):.2f}")

    # 分析高误差色块
    high_error_blocks = [(i, err) for i, err in enumerate(errors) if err > avg_error * 1.5]
    print(f"\n高误差色块 (误差 > {avg_error * 1.5:.1f}): {len(high_error_blocks)} 个")
    for i, err in high_error_blocks[:10]:  # 显示前10个
        print(f"  色块 {i}: 误差 {err:.1f}")

    # 与目标比较
    print(f"\n与目标比较:")
    print(f"目标误差: < 5.0")
    print(f"当前误差: {avg_error:.2f}")
    if avg_error <= 5:
        print("🎉 恭喜！已达到目标误差范围！")
    else:
        print(f"还需要进一步优化，差距: {avg_error - 5:.2f}")

    return corrected_image


def determine_image_orientation(corners, ids):
    """
    判断图像方向并返回需要的旋转角度
    """
    # 将corners和ids组合并按y坐标排序
    markers = []
    for corner, id_num in zip(corners, ids.flatten()):
        # 计算ArUco码的中心点
        center_x = np.mean(corner[0][:, 0])
        center_y = np.mean(corner[0][:, 1])
        markers.append((corner[0], id_num, center_x, center_y))
    
    # 找到ID为0的标记
    marker_0 = None
    marker_1 = None
    for marker in markers:
        if marker[1] == 0:
            marker_0 = marker
        elif marker[1] == 1:
            marker_1 = marker
    
    # 计算ID 0标记的方向
    corner_0 = marker_0[0]  # 获取ID 0的角点
    # ArUco码角点顺序：左上、右上、右下、左下
    top_left = corner_0[0]
    top_right = corner_0[1]
    bottom_right = corner_0[2]
    bottom_left = corner_0[3]
    
    # 计算当前方向
    dx = top_right[0] - top_left[0]
    dy = top_right[1] - top_left[1]
    current_angle = np.degrees(np.arctan2(dy, dx))
    
    # 如果有ID 1，用它来确认方向
    if marker_1 is not None:
        # 检查ID 1是否在ID 0的右边
        if marker_1[2] < marker_0[2]:  # 如果ID 1在ID 0的左边
            current_angle += 180
    
    # 计算需要旋转的角度
    if -45 <= current_angle <= 45:  # 大致水平
        return 0
    elif 45 < current_angle <= 135:  # 需要逆时针旋转
        return 90
    elif -135 <= current_angle < -45:  # 需要顺时针旋转
        return -90
    else:  # 需要旋转180度
        return 180

def rotate_image(image, angle):
    """
    旋转图像，确保不裁剪边缘内容
    """
    if angle == 0:
        return image
    
    # 获取图像尺寸
    height, width = image.shape[:2]
    
    # 计算旋转后的图像大小
    # 将角度转换为弧度
    angle_rad = np.abs(np.radians(angle))
    
    # 计算旋转后的新尺寸
    new_width = int(width * np.abs(np.cos(angle_rad)) + height * np.abs(np.sin(angle_rad)))
    new_height = int(width * np.abs(np.sin(angle_rad)) + height * np.abs(np.cos(angle_rad)))
    
    # 计算新的中心点
    center_x = width // 2
    center_y = height // 2
    
    # 获取旋转矩阵
    rotation_matrix = cv2.getRotationMatrix2D((center_x, center_y), angle, 1.0)
    
    # 调整旋转矩阵的平移部分，确保整个图像都在视野内
    rotation_matrix[0, 2] += (new_width - width) // 2
    rotation_matrix[1, 2] += (new_height - height) // 2
    
    # 执行旋转，使用新的尺寸
    rotated_image = cv2.warpAffine(
        image, 
        rotation_matrix, 
        (new_width, new_height),
        flags=cv2.INTER_LINEAR,
        borderMode=cv2.BORDER_CONSTANT,
        borderValue=(255, 255, 255)  # 使用白色填充边界
    )
    
    return rotated_image

def detect_test_strip_area(image, output_size=(800, 600)):
    """
    检测并标注尿试纸检测区域
    
    Args:
        image: 校正后的图像
        output_size: 标准化后的图像大小
    Returns:
        annotated_image: 标注后的图像
        strip_area: 提取的试纸区域
    """
    # 黑色长框的固定坐标 (基于800x600的标准化图像)
    x = 100  # 左边界
    y = 270  # 上边界
    w = 600  # 宽度
    h = 60   # 高度
    
    # 复制原图用于标注
    annotated_image = image.copy()
    
    # 提取试纸区域
    strip_area = image[y:y+h, x:x+w]
    
    # 在图像上标注检测区域
    cv2.rectangle(annotated_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
    cv2.putText(annotated_image, 'Test Strip Area', (x, y - 10),
                cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
    
    # 显示调试信息
    print(f"检测区域坐标: x={x}, y={y}, w={w}, h={h}")
    
    return annotated_image, strip_area

def detect_colored_region(strip_area):
    """
    检测试纸上的显色区域
    """
    # 转换到HSV颜色空间
    hsv_image = cv2.cvtColor(strip_area, cv2.COLOR_BGR2HSV)
    
    # 分离通道
    h, s, v = cv2.split(hsv_image)
    
    # 使用饱和度通道来检测颜色变化
    s_blurred = cv2.GaussianBlur(s, (5, 5), 0)
    
    # 使用Otsu阈值分割
    _, binary = cv2.threshold(s_blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # 显示二值化结果
    cv2.imshow('Binary Threshold Result', binary)
    cv2.waitKey(1)  # 不阻塞

    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5,5))
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

    # 显示形态学处理后的二值图
    cv2.imshow('Morphological Result', binary)
    cv2.waitKey(1)  # 不阻塞

    # 找到轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 在原图上绘制所有轮廓
    debug_image = strip_area.copy()
    cv2.drawContours(debug_image, contours, -1, (0,255,0), 1)
    cv2.imshow('All Contours', debug_image)
    cv2.waitKey(1)  # 不阻塞
    
    # 直接返回最大的轮廓区域
    if not contours:
        print("未找到轮廓")
        return None, strip_area.copy()
    
    # 找到最大的轮廓
    largest_contour = max(contours, key=cv2.contourArea)
    x, y, w, h = cv2.boundingRect(largest_contour)
    
    # 计算中心3/4区域的坐标
    quarter_w = w // 8  # 1/8宽度
    quarter_h = h // 8  # 1/8高度
    center_x1 = x + quarter_w
    center_y1 = y + quarter_h
    center_x2 = x + w - quarter_w
    center_y2 = y + h - quarter_h
    
    # 创建可视化图像
    visualized_image = strip_area.copy()
    
    # 绘制完整区域（红色）和采样区域（蓝色）
    cv2.rectangle(visualized_image, (x, y), (x+w, y+h), (0, 0, 255), 2)  # 完整区域
    cv2.rectangle(visualized_image,
                 (center_x1, center_y1),
                 (center_x2, center_y2),
                 (255, 0, 0), 1)  # 采样区域
    
    # 返回中心3/4区域的坐标，而不是整个区域
    detected_region = (center_x1, center_y1, center_x2-center_x1, center_y2-center_y1)
    
    print(f"检测到的显色区域: x={x}, y={y}, w={w}, h={h}")
    print(f"采样区域: x={center_x1}, y={center_y1}, w={center_x2-center_x1}, h={center_y2-center_y1}")
    
    return detected_region, visualized_image

def process_rotated_image(captured_image):
    """处理旋转后的图像"""
    detected_corners = get_aruco_corners(captured_image, [0, 1, 2, 3])
    
    if not detected_corners:
        print("旋转后未能检测到ArUco码，尝试调整图像...")
        captured_image = crop_white_borders(captured_image)
        detected_corners = get_aruco_corners(captured_image, [0, 1, 2, 3])
        
        if not detected_corners:
            raise ValueError("裁剪后仍未能检测到ArUco码")
    
    return captured_image, detected_corners

def crop_white_borders(image):
    """裁剪图像白边"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 250, 255, cv2.THRESH_BINARY_INV)
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        raise ValueError("无法找到图像主体区域")
    
    # 找到最大的轮廓（应该是图像的主体部分）
    main_contour = max(contours, key=cv2.contourArea)
    x, y, w, h = cv2.boundingRect(main_contour)
    
    # 添加边距
    margin = 20
    x = max(0, x - margin)
    y = max(0, y - margin)
    w = min(image.shape[1] - x, w + 2*margin)
    h = min(image.shape[0] - y, h + 2*margin)
    
    return image[y:y+h, x:x+w]

def process_colored_region(transformed_image):
    """处理显色区域检测和分析"""
    # 检测试纸区域
    _, strip_area = detect_test_strip_area(transformed_image)
    
    # 检测显色区域
    colored_region, visualized_strip = detect_colored_region(strip_area)
    
    if colored_region is None:
        raise ValueError("未能检测到显色区域")
        
    return colored_region, visualized_strip

def display_image(title, image, size=(800, 600)):
    """显示图像的通用函数"""
    cv2.namedWindow(title, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(title, size[0], size[1])
    cv2.imshow(title, image)
    cv2.waitKey(1)  # 改为1毫秒，不阻塞程序
    # 不立即销毁窗口，让用户可以看到结果

# def calculate_color_consistency(detected_values, true_values, indices):
#     """
#     计算色彩一致性约束
    
#     Args:
#         detected_values: 检测到的LAB值数组
#         true_values: 真实LAB值数组
#         indices: 色块索引
    
#     Returns:
#         similar_pairs: 相似色块对的列表
#     """
#     similar_pairs = []
    
#     # 计算真实LAB颜色之间的相似度
#     for i in range(len(true_values)):
#         for j in range(i+1, len(true_values)):
#             # 计算真实LAB颜色之间的欧氏距离
#             true_dist = np.linalg.norm(true_values[i] - true_values[j])
            
#             # 如果真实颜色足够接近(相似色)
#             if true_dist < 15:
#                 # 获取对应的色块索引
#                 idx_i = indices[i]
#                 idx_j = indices[j]
#                 similar_pairs.append((idx_i, idx_j))
#                 print(f"发现相似色块对: {idx_i}和{idx_j}, 真实颜色距离: {true_dist:.2f}")
    
#     return similar_pairs

# def adaptive_color_correction(image, true_rgb_values):
#     """
#     使用精密颜色校正算法 v4 进行颜色校正

#     Args:
#         image: BGR格式的输入图像
#         true_rgb_values: 72色比色卡的真实RGB值字典
#     Returns:
#         corrected_image: 颜色校正后的图像
#     """
#     # 1. 色块提取
#     gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
#     edges = cv2.Canny(gray, 50, 150)
#     color_blocks, debug_image = extract_color_blocks_from_edges(
#         edges, image,
#         min_width=40, max_width=50,
#         min_height=40, max_height=50
#     )

#     # 2. 网格位置匹配验证
#     corrected_blocks = validate_grid_matching(color_blocks)

#     # 3. 使用精密颜色校正算法 v5
#     print("\n使用精密颜色校正算法 v5...")
#     corrected_image = precision_color_correction_v5(image, corrected_blocks, true_rgb_values)

#     return corrected_image

# def spatial_smoothing(lab_image, problem_indices=[31, 37, 54, 55]):
#     """
#     对LAB空间图像应用空间平滑处理，尤其针对问题色块区域
    
#     Args:
#         lab_image: LAB格式的图像数据
#         problem_indices: 问题色块索引列表
    
#     Returns:
#         smoothed_image: 平滑处理后的图像
#     """
#     height, width = lab_image.shape[:2]
#     smoothed_image = lab_image.copy()
    
#     # 提取L, a, b通道
#     l_channel = smoothed_image[:, :, 0]
#     a_channel = smoothed_image[:, :, 1]
#     b_channel = smoothed_image[:, :, 2]
    
#     # 只对a和b通道应用平滑滤波
#     kernel_size = 3
#     a_channel = cv2.GaussianBlur(a_channel, (kernel_size, kernel_size), 0)
#     b_channel = cv2.GaussianBlur(b_channel, (kernel_size, kernel_size), 0)
    
#     # 合并回图像
#     smoothed_image[:, :, 1] = a_channel
#     smoothed_image[:, :, 2] = b_channel
    
#     # 对问题区域进行额外平滑
#     if len(problem_indices) > 0:
#         # 这里假设您有一个函数可以从色块索引获取对应区域的掩码
#         # 如果没有，可以在验证校正结果环节处理问题区域
#         pass
    
#     return smoothed_image

def main():
    try:
        # 加载图像 - 检查多个可能的路径
        possible_paths = [
            r"D:\Python-learn\6.19niaodanbai\400.2.jpg"
        ]

        captured_image = None
        captured_image_path = None

        for path in possible_paths:
            if os.path.exists(path):
                captured_image = cv2.imread(path)
                if captured_image is not None:
                    captured_image_path = path
                    print(f"成功加载图像: {path}")
                    break

        if captured_image is None:
            print("未找到测试图像，请提供图像文件路径")
            print("支持的路径:")
            for path in possible_paths:
                print(f"  - {path}")
            return

        # 显示原始图像
        display_image('Original Image', captured_image)

        # 应用最优预处理（基于实验验证的双边滤波降噪）
        captured_image = optimal_preprocessing(captured_image)

        # 检测ArUco码
        detected_corners = get_aruco_corners(captured_image, [0, 1, 2, 3])
        if not detected_corners:
            raise ValueError("未检测到任何ArUco码")

        # 转换检测结果格式
        corners = [[corner] for _, corner in detected_corners.items()]
        ids = np.array([[id_num] for id_num in detected_corners.keys()])

        # 确定方向并旋转
        rotation_angle = determine_image_orientation(corners, ids)
        print(f"需要旋转的角度: {rotation_angle}")

        if rotation_angle != 0:
            captured_image = rotate_image(captured_image, rotation_angle)
            captured_image, detected_corners = process_rotated_image(captured_image)

        # 显示方向校正后的图像
        display_image('Orientation Corrected Image', captured_image)

        # 透视变换
        target_aruco_ids = [0, 1, 2, 3]
        output_size = (800, 600)
        transformed_image = four_point_transform(captured_image, target_aruco_ids, output_size)
        if transformed_image is None:
            raise ValueError("透视变换失败")

        # 显示透视变换结果
        display_image('Warped Image', transformed_image, output_size)

        # 环境光补偿
        print("\n执行环境光补偿...")
        compensated_image, illumination_info = compensate_illumination(transformed_image)
        
        # 处理显色区域 - 在颜色校正前
        colored_region_before, visualized_strip_before = process_colored_region(compensated_image)
        
        # 计算显色区域校正前的绝对位置
        region_x, region_y, region_w, region_h = colored_region_before
        strip_x, strip_y = 100, 270
        absolute_x = strip_x + region_x
        absolute_y = strip_y + region_y
        
        # 获取校正前的RGB值
        uncorrected_region = compensated_image[
            absolute_y:absolute_y + region_h,
            absolute_x:absolute_x + region_w
        ]
        mean_bgr_before = cv2.mean(uncorrected_region)[:3]
        mean_rgb_before = mean_bgr_before[::-1]  # 转换为RGB

        # 提取色块
        gray = cv2.cvtColor(compensated_image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        color_blocks, _ = extract_color_blocks_from_edges(
            edges, compensated_image, 
            min_width=40, max_width=50, 
            min_height=40, max_height=50
        )
        
        # 网格位置匹配验证
        corrected_blocks = validate_grid_matching(color_blocks)
        
        # 使用精密颜色校正算法 v5 替代原来的LAB校正
        corrected_image = precision_color_correction_v5(compensated_image, corrected_blocks, true_rgb_values)

        # 处理显色区域 - 在颜色校正后
        colored_region, visualized_strip = process_colored_region(corrected_image)
        display_image('Colored Region Detection', visualized_strip, (600, 60))

        # 计算显色区域校正后的绝对位置和RGB值
        region_x, region_y, region_w, region_h = colored_region
        absolute_x = strip_x + region_x
        absolute_y = strip_y + region_y

        corrected_region = corrected_image[
            absolute_y:absolute_y + region_h,
            absolute_x:absolute_x + region_w
        ]

        # 计算并显示结果
        mean_bgr = cv2.mean(corrected_region)[:3]
        mean_rgb = mean_bgr[::-1]  # 转换为RGB
        
        print("\n显色区域信息:")
        print(f"位置: x={absolute_x}, y={absolute_y}, w={region_w}, h={region_h}")
        print(f"校正前的RGB值: {tuple(map(int, mean_rgb_before))}")
        print(f"校正后的RGB值: {tuple(map(int, mean_rgb))}")
        print(f"RGB值变化: R{int(mean_rgb[0]-mean_rgb_before[0]):+d}, "
              f"G{int(mean_rgb[1]-mean_rgb_before[1]):+d}, "
              f"B{int(mean_rgb[2]-mean_rgb_before[2]):+d}")
        
        # 打印环境光信息
        print("\n环境光信息:")
        print(f"估计的环境光: {tuple(map(int, illumination_info['estimated_illumination']))}")
        print(f"白平衡增益: {tuple(map(lambda x: round(x, 2), illumination_info['white_balance_gains']))}")

        # 标注最终结果
        final_annotated = corrected_image.copy()
        cv2.rectangle(
            final_annotated,
            (absolute_x, absolute_y),
            (absolute_x + region_w, absolute_y + region_h),
            (0, 0, 255),
            2
        )
        
        # 在图像上标注RGB值
        text_y = absolute_y - 10
        cv2.putText(final_annotated, 
                   f"Before: RGB={tuple(map(int, mean_rgb_before))}", 
                   (absolute_x, text_y), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        cv2.putText(final_annotated, 
                   f"After: RGB={tuple(map(int, mean_rgb))}", 
                   (absolute_x, text_y + 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        
        # 显示对比图
        h1, w1 = compensated_image.shape[:2]
        h2, w2 = corrected_image.shape[:2]
        comparison = np.zeros((max(h1, h2), w1 + w2, 3), dtype=np.uint8)
        comparison[:h1, :w1] = compensated_image
        comparison[:h2, w1:w1+w2] = corrected_image
        
        # 添加标签
        cv2.putText(comparison, "Before Correction", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        cv2.putText(comparison, "After Correction", (w1 + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        
        display_image('Before vs After Statistical Correction', comparison, (1600, 600))
        display_image('Final Result', final_annotated, (800, 600))

    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        cv2.destroyAllWindows()

if __name__ == "__main__":
    main() 
    # 12在6.27号的复制黏贴版，想让cursor全自动运行，直到校正到一个理想的状态
    # 加了一个双边滤波，31行代码