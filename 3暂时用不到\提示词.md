**编写Python代码时，请严格按照以下规范执行：**

**核心规范(必须遵循)：**

- 4空格缩进，禁用Tab

- 行长度≤88字符

- snake_case命名变量/函数，PascalCase命名类

- 优先使用f-string格式化

**建议规范:**

- 多行集合未尾加逗号
- 常量全大写命名
- 遵循PEP 8导入顺序

**编写 HTML 代码时，请严格按照以下规范执行：**

**核心规范 (必须遵循)：**

- **缩进与空格**
  - 使用 2 个空格缩进，禁用 Tab
  - 标签和属性使用小写字母
  - 自闭合标签需添加斜杠（如`<img src="" alt="" />`）
- **行长度与格式**
  - 每行代码长度≤120 字符
  - 复杂元素的子标签应分行书写
  - 使用双引号包裹属性值
- **语义化与结构**
  - 优先使用语义化标签（`<header>`, `<nav>`, `<main>`, `<footer>`等）
  - 避免使用无意义的 div 嵌套（如`<div class="header">` → `<header>`）
  - 保持标签层级扁平，避免过深嵌套
- **资源引用**
  - 外部资源（CSS/JS）使用相对路径
  - 图片必须添加`alt`属性描述内容
  - 避免内联 CSS/JS，优先分离到独立文件

**建议规范：**

- **注释与组织**
  - 使用`<!-- 区块注释 -->`划分页面逻辑
  - 避免在注释中重复显而易见的代码功能
- **性能优化**
  - CSS 文件通过`<link>`引入，置于`<head>`中
  - JS 文件通过`<script>`引入，使用`defer`或`async`属性
  - 图片使用现代格式（WebP）并添加`srcset`响应式支持
- **可访问性**
  - 表单元素必须使用`<label>`关联
  - 按钮使用`<button>`而非`<div>`模拟
  - ARIA 角色用于增强非语义化元素的可访问性
- **安全最佳实践**
  - 避免使用内联样式（style 属性）
  - 外部脚本使用`integrity`属性验证完整性
  - 设置 Content Security Policy（CSP）元标签



**请在代码中体现这些规范，如有冲突以核心规范为准。**