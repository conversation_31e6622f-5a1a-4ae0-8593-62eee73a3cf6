import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 72个色块的数据
data = [
    (0, (19, 18, 17), (20, 20, 18), (69, 71, 71)),
    (1, (26, 23, 22), (28, 25, 24), (77, 78, 80)),
    (2, (46, 38, 40), (50, 42, 45), (101, 105, 110)),
    (3, (96, 86, 88), (109, 98, 101), (138, 144, 151)),
    (4, (140, 135, 137), (159, 155, 159), (171, 180, 190)),
    (5, (174, 168, 165), (200, 195, 193), (211, 212, 218)),
    (6, (188, 185, 177), (216, 215, 208), (229, 231, 232)),
    (7, (169, 162, 160), (194, 188, 187), (203, 206, 211)),
    (8, (98, 113, 113), (110, 129, 131), (135, 165, 170)),
    (9, (66, 56, 59), (73, 63, 66), (118, 124, 130)),
    (10, (36, 28, 29), (39, 31, 32), (93, 95, 98)),
    (11, (22, 19, 18), (23, 21, 20), (74, 76, 76)),
    (12, (178, 149, 118), (204, 173, 138), (212, 196, 174)),
    (13, (164, 126, 97), (188, 146, 112), (195, 177, 155)),
    (14, (137, 102, 71), (157, 118, 84), (173, 159, 135)),
    (15, (120, 85, 53), (137, 98, 62), (162, 147, 123)),
    (16, (99, 65, 38), (112, 73, 44), (144, 133, 111)),
    (17, (77, 44, 27), (86, 50, 31), (130, 116, 95)),
    (18, (60, 33, 21), (66, 37, 23), (117, 104, 83)),
    (19, (45, 26, 16), (48, 28, 17), (103, 92, 74)),
    (20, (41, 24, 15), (44, 26, 16), (99, 89, 72)),
    (21, (38, 23, 15), (40, 24, 16), (93, 84, 72)),
    (22, (34, 22, 15), (36, 23, 16), (90, 82, 71)),
    (23, (30, 21, 16), (32, 23, 17), (85, 78, 71)),
    (24, (183, 169, 136), (211, 197, 159), (222, 216, 187)),
    (25, (167, 161, 119), (193, 188, 140), (205, 207, 170)),
    (26, (149, 143, 83), (171, 166, 98), (185, 191, 148)),
    (27, (133, 124, 52), (152, 144, 60), (170, 175, 123)),
    (28, (112, 103, 23), (127, 118, 26), (156, 159, 99)),
    (29, (96, 82, 14), (108, 93, 15), (145, 144, 80)),
    (30, (75, 62, 10), (83, 70, 11), (129, 130, 70)),
    (31, (50, 46, 9), (55, 51, 10), (111, 117, 71)),
    (32, (141, 111, 99), (161, 127, 115), (174, 161, 155)),
    (33, (125, 84, 69), (142, 96, 80), (161, 143, 135)),
    (34, (101, 49, 40), (113, 55, 46), (145, 118, 109)),
    (35, (79, 28, 22), (87, 31, 25), (129, 95, 89)),
    (36, (191, 148, 151), (219, 171, 177), (221, 193, 201)),
    (37, (179, 109, 114), (205, 126, 135), (206, 162, 166)),
    (38, (152, 57, 69), (174, 66, 81), (182, 123, 131)),
    (39, (137, 29, 32), (156, 33, 38), (165, 101, 103)),
    (40, (116, 16, 16), (130, 18, 18), (149, 80, 79)),
    (41, (89, 14, 10), (99, 15, 12), (132, 73, 67)),
    (42, (74, 14, 11), (80, 15, 13), (120, 73, 69)),
    (43, (62, 13, 16), (66, 13, 17), (107, 71, 76)),
    (44, (113, 57, 51), (127, 65, 59), (151, 126, 120)),
    (45, (86, 30, 25), (97, 34, 29), (134, 100, 93)),
    (46, (70, 22, 17), (76, 24, 18), (123, 88, 78)),
    (47, (58, 18, 14), (61, 19, 15), (112, 78, 70)),
    (48, (139, 163, 178), (158, 189, 208), (168, 203, 218)),
    (49, (118, 148, 171), (135, 172, 201), (151, 189, 210)),
    (50, (102, 128, 163), (116, 148, 190), (136, 172, 199)),
    (51, (77, 105, 155), (87, 122, 181), (118, 156, 189)),
    (52, (57, 84, 145), (65, 97, 170), (106, 142, 179)),
    (53, (36, 59, 130), (41, 67, 151), (91, 126, 169)),
    (54, (24, 39, 111), (27, 44, 128), (79, 109, 156)),
    (55, (17, 30, 97), (19, 33, 111), (68, 100, 148)),
    (56, (87, 112, 101), (99, 129, 118), (129, 163, 158)),
    (57, (68, 95, 83), (77, 109, 96), (117, 150, 143)),
    (58, (51, 76, 64), (57, 86, 73), (104, 137, 128)),
    (59, (35, 52, 44), (39, 58, 49), (92, 121, 113)),
    (60, (18, 18, 18), (19, 19, 20), (68, 70, 71)),
    (61, (178, 174, 175), (205, 202, 205), (211, 214, 219)),
    (62, (143, 141, 148), (163, 163, 172), (173, 182, 190)),
    (63, (43, 37, 42), (48, 41, 47), (100, 105, 112)),
    (64, (177, 154, 126), (204, 179, 149), (212, 196, 169)),
    (65, (181, 172, 141), (209, 200, 167), (222, 216, 186)),
    (66, (191, 149, 153), (219, 173, 180), (221, 192, 200)),
    (67, (138, 163, 176), (158, 190, 208), (169, 202, 217)),
    (68, (108, 56, 51), (123, 64, 58), (149, 124, 119)),
    (69, (88, 113, 101), (100, 130, 118), (130, 162, 153)),
    (70, (142, 114, 103), (162, 132, 121), (174, 163, 156)),
    (71, (65, 59, 67), (72, 65, 75), (118, 126, 132))
]

# 提取数据
indices = [item[0] for item in data]
white_balance_before = [item[1] for item in data]  # 白平衡前RGB
correction_before = [item[2] for item in data]     # 校正前RGB（白平衡后）
true_rgb = [item[3] for item in data]              # 真实RGB

# 分离RGB通道
wb_before_r = [rgb[0] for rgb in white_balance_before]
wb_before_g = [rgb[1] for rgb in white_balance_before]
wb_before_b = [rgb[2] for rgb in white_balance_before]

corr_before_r = [rgb[0] for rgb in correction_before]
corr_before_g = [rgb[1] for rgb in correction_before]
corr_before_b = [rgb[2] for rgb in correction_before]

true_r = [rgb[0] for rgb in true_rgb]
true_g = [rgb[1] for rgb in true_rgb]
true_b = [rgb[2] for rgb in true_rgb]

# 创建图形
fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 8))
fig.suptitle('72色块RGB值变化分析', fontsize=16, fontweight='bold')

# R通道图
ax1.plot(indices, wb_before_r, 'r-', linewidth=2, label='白平衡前', alpha=0.8, marker='o', markersize=4, markerfacecolor='black', markeredgecolor='black')
ax1.plot(indices, corr_before_r, 'r--', linewidth=2, label='校正前', alpha=0.8, marker='s', markersize=3, markerfacecolor='black', markeredgecolor='black')
ax1.plot(indices, true_r, 'r:', linewidth=3, label='真实值', alpha=0.9, marker='^', markersize=3, markerfacecolor='black', markeredgecolor='black')
ax1.set_title('R通道', fontsize=14, fontweight='bold')
ax1.set_ylabel('R值', fontsize=12)
ax1.legend(fontsize=10)
ax1.grid(True, alpha=0.3)
ax1.set_ylim(0, 250)

# G通道图
ax2.plot(indices, wb_before_g, 'g-', linewidth=2, label='白平衡前', alpha=0.8, marker='o', markersize=4, markerfacecolor='black', markeredgecolor='black')
ax2.plot(indices, corr_before_g, 'g--', linewidth=2, label='校正前', alpha=0.8, marker='s', markersize=3, markerfacecolor='black', markeredgecolor='black')
ax2.plot(indices, true_g, 'g:', linewidth=3, label='真实值', alpha=0.9, marker='^', markersize=3, markerfacecolor='black', markeredgecolor='black')
ax2.set_title('G通道', fontsize=14, fontweight='bold')
ax2.set_ylabel('G值', fontsize=12)
ax2.legend(fontsize=10)
ax2.grid(True, alpha=0.3)
ax2.set_ylim(0, 250)

# B通道图
ax3.plot(indices, wb_before_b, 'b-', linewidth=2, label='白平衡前', alpha=0.8, marker='o', markersize=4, markerfacecolor='black', markeredgecolor='black')
ax3.plot(indices, corr_before_b, 'b--', linewidth=2, label='校正前', alpha=0.8, marker='s', markersize=3, markerfacecolor='black', markeredgecolor='black')
ax3.plot(indices, true_b, 'b:', linewidth=3, label='真实值', alpha=0.9, marker='^', markersize=3, markerfacecolor='black', markeredgecolor='black')
ax3.set_title('B通道', fontsize=14, fontweight='bold')
ax3.set_xlabel('色块序号', fontsize=12)
ax3.set_ylabel('B值', fontsize=12)
ax3.legend(fontsize=10)
ax3.grid(True, alpha=0.3)
ax3.set_ylim(0, 250)

# 调整布局
plt.tight_layout()

# 保存图片
plt.savefig('RGB_72色块分析图_2025-01-23.png', dpi=150, bbox_inches='tight',
            facecolor='white', edgecolor='none')
print("✅ RGB折线图已保存为: RGB_72色块分析图_2025-01-23.png")

# 显示原始图形
plt.show()  # 手动关闭

# 打印数据统计信息
print("\n=== 数据统计分析 ===")
print(f"总色块数量: {len(data)}")
print(f"R通道范围 - 白平衡前: {min(wb_before_r)}-{max(wb_before_r)}, 真实值: {min(true_r)}-{max(true_r)}")
print(f"G通道范围 - 白平衡前: {min(wb_before_g)}-{max(wb_before_g)}, 真实值: {min(true_g)}-{max(true_g)}")
print(f"B通道范围 - 白平衡前: {min(wb_before_b)}-{max(wb_before_b)}, 真实值: {min(true_b)}-{max(true_b)}")
print("📊 原始图表已生成，显示了72个色块的RGB变化趋势")

print("\n正在生成按RGB值排序的图表...")

# ==================== 按RGB值排序的图表 ====================

def create_sorted_data(channel_data, wb_data, corr_data, true_data):
    """创建按指定通道排序的数据 - 确保严格递增"""
    # 将数据组合并按真实值排序
    combined = list(zip(range(len(channel_data)), wb_data, corr_data, true_data))
    sorted_combined = sorted(combined, key=lambda x: x[3])  # 按真实值排序

    sorted_indices = [x[0] for x in sorted_combined]
    sorted_wb = [x[1] for x in sorted_combined]
    sorted_corr = [x[2] for x in sorted_combined]
    sorted_true = [x[3] for x in sorted_combined]

    # 验证排序结果
    print(f"排序验证 - 最小值: {min(sorted_true)}, 最大值: {max(sorted_true)}")
    print(f"是否严格递增: {all(sorted_true[i] <= sorted_true[i+1] for i in range(len(sorted_true)-1))}")

    return sorted_indices, sorted_wb, sorted_corr, sorted_true

# 为每个通道创建排序数据
r_sorted_indices, r_sorted_wb, r_sorted_corr, r_sorted_true = create_sorted_data(
    wb_before_r, wb_before_r, corr_before_r, true_r)
g_sorted_indices, g_sorted_wb, g_sorted_corr, g_sorted_true = create_sorted_data(
    wb_before_g, wb_before_g, corr_before_g, true_g)
b_sorted_indices, b_sorted_wb, b_sorted_corr, b_sorted_true = create_sorted_data(
    wb_before_b, wb_before_b, corr_before_b, true_b)

# 创建排序后的图形
fig2, (ax1_s, ax2_s, ax3_s) = plt.subplots(3, 1, figsize=(12, 8))
fig2.suptitle('72色块RGB值排序分析（按真实值从小到大严格排列）', fontsize=16, fontweight='bold')

# R通道排序图
x_pos = range(len(r_sorted_true))
ax1_s.plot(x_pos, r_sorted_wb, 'r-', linewidth=2, label='白平衡前', alpha=0.8, marker='o', markersize=4, markerfacecolor='black', markeredgecolor='black')
ax1_s.plot(x_pos, r_sorted_corr, 'r--', linewidth=2, label='校正前', alpha=0.8, marker='s', markersize=3, markerfacecolor='black', markeredgecolor='black')
ax1_s.plot(x_pos, r_sorted_true, 'r:', linewidth=3, label='真实值', alpha=0.9, marker='^', markersize=3, markerfacecolor='black', markeredgecolor='black')
ax1_s.set_title('R通道（按真实值排序）', fontsize=14, fontweight='bold')
ax1_s.set_ylabel('R值', fontsize=12)
ax1_s.legend(fontsize=10)
ax1_s.grid(True, alpha=0.3)
ax1_s.set_ylim(0, 250)

# G通道排序图
ax2_s.plot(x_pos, g_sorted_wb, 'g-', linewidth=2, label='白平衡前', alpha=0.8, marker='o', markersize=4, markerfacecolor='black', markeredgecolor='black')
ax2_s.plot(x_pos, g_sorted_corr, 'g--', linewidth=2, label='校正前', alpha=0.8, marker='s', markersize=3, markerfacecolor='black', markeredgecolor='black')
ax2_s.plot(x_pos, g_sorted_true, 'g:', linewidth=3, label='真实值', alpha=0.9, marker='^', markersize=3, markerfacecolor='black', markeredgecolor='black')
ax2_s.set_title('G通道（按真实值排序）', fontsize=14, fontweight='bold')
ax2_s.set_ylabel('G值', fontsize=12)
ax2_s.legend(fontsize=10)
ax2_s.grid(True, alpha=0.3)
ax2_s.set_ylim(0, 250)

# B通道排序图
ax3_s.plot(x_pos, b_sorted_wb, 'b-', linewidth=2, label='白平衡前', alpha=0.8, marker='o', markersize=4, markerfacecolor='black', markeredgecolor='black')
ax3_s.plot(x_pos, b_sorted_corr, 'b--', linewidth=2, label='校正前', alpha=0.8, marker='s', markersize=3, markerfacecolor='black', markeredgecolor='black')
ax3_s.plot(x_pos, b_sorted_true, 'b:', linewidth=3, label='真实值', alpha=0.9, marker='^', markersize=3, markerfacecolor='black', markeredgecolor='black')
ax3_s.set_title('B通道（按真实值排序）', fontsize=14, fontweight='bold')
ax3_s.set_xlabel('排序位置（从小到大）', fontsize=12)
ax3_s.set_ylabel('B值', fontsize=12)
ax3_s.legend(fontsize=10)
ax3_s.grid(True, alpha=0.3)
ax3_s.set_ylim(0, 250)

# 调整布局
plt.tight_layout()

# 保存排序图片
plt.savefig('RGB_72色块排序分析图_2025-07-23.png', dpi=150, bbox_inches='tight',
            facecolor='white', edgecolor='none')
print("✅ RGB排序折线图已保存为: RGB_72色块排序分析图_2025-07-23.png")

# 显示排序图形
plt.show()  # 手动关闭

print("\n=== 排序分析说明 ===")
print("📈 排序图表特点：")
print("   • 横轴：按真实RGB值从小到大的严格排序位置")
print("   • 纵轴：对应的RGB值")
print("   • 真实值线呈现完全单调递增趋势（无起伏）")
print("   • 可清楚观察白平衡和校正前值与真实值的偏差模式")
print("   • 观察RGB值分布的连续性和线性度")
print("   • 识别特定RGB范围内的校正性能")
print("   • 发现系统性的偏差趋势")
print("   • 评估算法在不同亮度区间的表现")
print("   • 验证排序算法的正确性（真实值必须严格递增）")

# 输出每个x轴对应的具体数值
print("\n" + "="*80)
print("72色块RGB值详细数据")
print("="*80)

print("\n【原始色块顺序数据】")
print("-"*60)
print(f"{'色块序号':<8} {'白平衡前R':<10} {'校正前R':<8} {'真实R':<8} {'白平衡前G':<10} {'校正前G':<8} {'真实G':<8} {'白平衡前B':<10} {'校正前B':<8} {'真实B':<8}")
print("-"*60)
for i in range(len(indices)):
    print(f"{indices[i]:<8} {wb_before_r[i]:<10.1f} {corr_before_r[i]:<8.1f} {true_r[i]:<8.1f} "
          f"{wb_before_g[i]:<10.1f} {corr_before_g[i]:<8.1f} {true_g[i]:<8.1f} "
          f"{wb_before_b[i]:<10.1f} {corr_before_b[i]:<8.1f} {true_b[i]:<8.1f}")

print("\n【按真实值排序后的数据】")
print("-"*60)
print(f"{'排序位置':<8} {'白平衡前R':<10} {'校正前R':<8} {'真实R':<8} {'白平衡前G':<10} {'校正前G':<8} {'真实G':<8} {'白平衡前B':<10} {'校正前B':<8} {'真实B':<8}")
print("-"*60)
for i in range(len(x_pos)):
    print(f"{x_pos[i]:<8} {r_sorted_wb[i]:<10.1f} {r_sorted_corr[i]:<8.1f} {r_sorted_true[i]:<8.1f} "
          f"{g_sorted_wb[i]:<10.1f} {g_sorted_corr[i]:<8.1f} {g_sorted_true[i]:<8.1f} "
          f"{b_sorted_wb[i]:<10.1f} {b_sorted_corr[i]:<8.1f} {b_sorted_true[i]:<8.1f}")

# ==================== 真实值RGB三通道对比图 ====================
print("\n正在生成真实值RGB三通道对比图...")

# 创建单一图表显示三个通道的真实值
fig3, ax = plt.subplots(1, 1, figsize=(12, 8))
fig3.suptitle('72色块真实值RGB三通道对比分析（按各通道真实值排序）', fontsize=16, fontweight='bold')

# 调试：打印前10个R通道排序值
print("前10个R通道排序值:", r_sorted_true[:10])
print("前10个G通道排序值:", g_sorted_true[:10])
print("前10个B通道排序值:", b_sorted_true[:10])

# 绘制三个通道的真实值线 - 使用细线条，无标记点
ax.plot(range(len(r_sorted_true)), r_sorted_true, 'r-', linewidth=2, label='R通道真实值', alpha=0.9)
ax.plot(range(len(g_sorted_true)), g_sorted_true, 'g-', linewidth=2, label='G通道真实值', alpha=0.9)
ax.plot(range(len(b_sorted_true)), b_sorted_true, 'b-', linewidth=2, label='B通道真实值', alpha=0.9)

ax.set_xlabel('排序位置（按各通道真实值从小到大）', fontsize=12)
ax.set_ylabel('RGB值', fontsize=12)
ax.set_title('三通道真实值分布对比', fontsize=14, fontweight='bold')
ax.grid(True, alpha=0.3)
ax.legend(fontsize=11)
ax.set_ylim(0, 250)  # 设置纵坐标范围为0-250

# 添加统计信息
ax.text(0.02, 0.98, f'R通道: {min(r_sorted_true)}-{max(r_sorted_true)}',
        transform=ax.transAxes, fontsize=10, verticalalignment='top',
        bbox=dict(boxstyle='round', facecolor='red', alpha=0.1))
ax.text(0.02, 0.92, f'G通道: {min(g_sorted_true)}-{max(g_sorted_true)}',
        transform=ax.transAxes, fontsize=10, verticalalignment='top',
        bbox=dict(boxstyle='round', facecolor='green', alpha=0.1))
ax.text(0.02, 0.86, f'B通道: {min(b_sorted_true)}-{max(b_sorted_true)}',
        transform=ax.transAxes, fontsize=10, verticalalignment='top',
        bbox=dict(boxstyle='round', facecolor='blue', alpha=0.1))

plt.tight_layout()
plt.savefig('RGB_三通道真实值对比图_2025-07-23.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"✅ RGB三通道对比图已保存为: RGB_三通道真实值对比图_2025-07-23.png")

# ==================== 三通道对比图排序数据 ====================
print("\n" + "="*80)
print("三通道真实值对比图 - 排序位置对应RGB值")
print("="*80)

print("\n【三通道真实值排序对照表】")
print("-"*80)
print(f"{'排序位置':<8} {'R通道真实值':<12} {'G通道真实值':<12} {'B通道真实值':<12} {'RGB组合':<15}")
print("-"*80)

for i in range(len(r_sorted_true)):
    rgb_combo = f"({r_sorted_true[i]},{g_sorted_true[i]},{b_sorted_true[i]})"
    print(f"{i:<8} {r_sorted_true[i]:<12.0f} {g_sorted_true[i]:<12.0f} {b_sorted_true[i]:<12.0f} {rgb_combo:<15}")

print("\n【三通道分布统计】")
print("-"*50)
print(f"R通道: 最小值={min(r_sorted_true)}, 最大值={max(r_sorted_true)}, 跨度={max(r_sorted_true)-min(r_sorted_true)}")
print(f"G通道: 最小值={min(g_sorted_true)}, 最大值={max(g_sorted_true)}, 跨度={max(g_sorted_true)-min(g_sorted_true)}")
print(f"B通道: 最小值={min(b_sorted_true)}, 最大值={max(b_sorted_true)}, 跨度={max(b_sorted_true)-min(b_sorted_true)}")

print("\n【关键观察点】")
print("-"*50)
print("📊 从三通道对比图可以观察到：")
print("   • 三条线的相对位置关系")
print("   • 哪些排序位置三个通道值接近（接近灰色）")
print("   • 哪些位置存在明显通道差异（有色彩偏向）")
print("   • 整体的RGB分布趋势和线性度")
print("   • 不同亮度区间的色彩平衡特征")
