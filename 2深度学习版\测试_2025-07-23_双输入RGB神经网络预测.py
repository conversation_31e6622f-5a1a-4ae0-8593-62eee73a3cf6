#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
双输入RGB神经网络预测真实值
使用白平衡前+白平衡后作为输入特征
PyTorch实现，包含dropout和正则化
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, TensorDataset
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

class DualInputRGBNet(nn.Module):
    """双输入RGB预测网络"""
    
    def __init__(self, input_dim=6, hidden_dims=[128, 64, 32], output_dim=3, dropout_rate=0.3):
        super(DualInputRGBNet, self).__init__()
        
        # 构建网络层
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),  # 批归一化
                nn.ReLU(),
                nn.Dropout(dropout_rate)     # Dropout正则化
            ])
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.network = nn.Sequential(*layers)
        
        # 权重初始化
        self._initialize_weights()
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        return self.network(x)

def prepare_data():
    """准备训练数据"""
    # 72色块的RGB数据（从之前的分析中获取）
    wb_before_r = np.array([19, 26, 46, 96, 140, 174, 188, 169, 98, 66, 36, 22, 178, 164, 137, 120, 99, 77, 60, 45, 41, 38, 34, 30, 183, 167, 149, 133, 112, 96, 75, 50, 141, 125, 101, 79, 191, 179, 152, 137, 116, 89, 74, 62, 113, 86, 70, 58, 139, 118, 102, 77, 57, 36, 24, 17, 87, 68, 51, 35, 18, 178, 143, 43, 177, 181, 191, 138, 108, 88, 142, 65], dtype=np.float32)
    
    wb_before_g = np.array([18, 23, 38, 86, 135, 168, 185, 162, 113, 56, 28, 19, 149, 126, 102, 85, 65, 44, 33, 26, 24, 23, 22, 21, 169, 161, 143, 124, 103, 82, 62, 46, 111, 84, 49, 28, 148, 109, 57, 29, 16, 14, 14, 13, 57, 30, 22, 18, 163, 148, 128, 105, 84, 59, 39, 30, 112, 95, 76, 52, 18, 174, 141, 37, 154, 172, 149, 163, 56, 113, 114, 59], dtype=np.float32)
    
    wb_before_b = np.array([17, 22, 40, 88, 137, 165, 177, 160, 113, 59, 29, 18, 118, 97, 71, 53, 38, 27, 21, 16, 15, 15, 15, 16, 136, 119, 83, 52, 23, 14, 10, 9, 99, 69, 40, 22, 151, 114, 69, 32, 16, 10, 11, 16, 51, 25, 17, 14, 178, 171, 163, 155, 145, 130, 111, 97, 101, 83, 64, 44, 18, 175, 148, 42, 126, 141, 153, 176, 51, 101, 103, 67], dtype=np.float32)
    
    corr_before_r = np.array([20, 28, 50, 109, 159, 200, 216, 194, 110, 73, 39, 23, 204, 188, 157, 137, 112, 86, 66, 48, 44, 40, 36, 32, 211, 193, 171, 152, 127, 108, 83, 55, 161, 142, 113, 87, 219, 205, 174, 156, 130, 99, 80, 66, 127, 97, 76, 61, 158, 135, 116, 87, 65, 41, 27, 19, 99, 77, 57, 39, 19, 205, 163, 48, 204, 209, 219, 158, 123, 100, 162, 72], dtype=np.float32)
    
    corr_before_g = np.array([20, 25, 42, 98, 155, 195, 215, 188, 129, 63, 31, 21, 173, 146, 118, 98, 73, 50, 37, 28, 26, 24, 23, 23, 197, 188, 166, 144, 118, 93, 70, 51, 127, 96, 55, 31, 171, 126, 66, 33, 18, 15, 15, 13, 65, 34, 24, 19, 189, 172, 148, 122, 97, 67, 44, 33, 129, 109, 86, 58, 19, 202, 163, 41, 179, 200, 173, 190, 64, 130, 132, 65], dtype=np.float32)
    
    corr_before_b = np.array([18, 24, 45, 101, 159, 193, 208, 187, 131, 66, 32, 20, 138, 112, 84, 62, 44, 31, 23, 17, 16, 16, 16, 17, 159, 140, 98, 60, 26, 15, 11, 10, 115, 80, 46, 25, 177, 135, 81, 38, 18, 12, 13, 17, 59, 29, 18, 15, 208, 201, 190, 181, 170, 151, 128, 111, 118, 96, 73, 49, 20, 205, 172, 47, 149, 167, 180, 208, 58, 118, 121, 75], dtype=np.float32)
    
    true_r = np.array([69, 77, 101, 138, 171, 211, 229, 203, 135, 118, 93, 74, 212, 195, 173, 162, 144, 130, 117, 103, 99, 93, 90, 85, 222, 205, 185, 170, 156, 145, 129, 111, 174, 161, 145, 129, 221, 206, 182, 165, 149, 132, 120, 107, 151, 134, 123, 112, 168, 151, 136, 118, 106, 91, 79, 68, 129, 117, 104, 92, 68, 211, 173, 100, 212, 222, 221, 169, 149, 130, 174, 118], dtype=np.float32)
    
    true_g = np.array([71, 78, 105, 144, 180, 212, 231, 206, 165, 124, 95, 76, 196, 177, 159, 147, 133, 116, 104, 92, 89, 84, 82, 78, 216, 207, 191, 175, 159, 144, 130, 117, 161, 143, 118, 95, 193, 162, 123, 101, 80, 73, 73, 71, 126, 100, 88, 78, 203, 189, 172, 156, 142, 126, 109, 100, 163, 150, 137, 121, 70, 214, 182, 105, 196, 216, 192, 202, 124, 162, 163, 126], dtype=np.float32)
    
    true_b = np.array([71, 80, 110, 151, 190, 218, 232, 211, 170, 130, 98, 76, 174, 155, 135, 123, 111, 95, 83, 74, 72, 72, 71, 71, 187, 170, 148, 123, 99, 80, 70, 71, 155, 135, 109, 89, 201, 166, 131, 103, 79, 67, 69, 76, 120, 93, 78, 70, 218, 210, 199, 189, 179, 169, 156, 148, 158, 143, 128, 113, 71, 219, 190, 112, 169, 186, 200, 217, 119, 153, 156, 132], dtype=np.float32)
    
    # 构建输入特征矩阵 [白平衡前RGB, 白平衡后RGB]
    X = np.column_stack([
        wb_before_r, wb_before_g, wb_before_b,  # 白平衡前
        corr_before_r, corr_before_g, corr_before_b  # 白平衡后
    ])
    
    # 构建目标矩阵
    y = np.column_stack([true_r, true_g, true_b])
    
    return X, y

def train_model(model, train_loader, val_loader, num_epochs=1000, learning_rate=0.001, weight_decay=1e-4):
    """训练模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    
    # 优化器和损失函数
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    criterion = nn.MSELoss()
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=50, verbose=True)
    
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 100
    
    print(f"开始训练，使用设备: {device}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        for batch_x, batch_y in train_loader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)
            
            optimizer.zero_grad()
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            train_loss += loss.item()
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                batch_x, batch_y = batch_x.to(device), batch_y.to(device)
                outputs = model(batch_x)
                loss = criterion(outputs, batch_y)
                val_loss += loss.item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), 'best_rgb_model.pth')
        else:
            patience_counter += 1
        
        if epoch % 100 == 0:
            print(f'Epoch [{epoch}/{num_epochs}], Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')
        
        if patience_counter >= patience:
            print(f"早停在第 {epoch} 轮，最佳验证损失: {best_val_loss:.6f}")
            break
    
    # 加载最佳模型
    model.load_state_dict(torch.load('best_rgb_model.pth'))
    
    return train_losses, val_losses

def evaluate_model(model, X_test, y_test):
    """评估模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()

    with torch.no_grad():
        X_test_tensor = torch.FloatTensor(X_test).to(device)
        predictions_tensor = model(X_test_tensor).cpu()
        # 安全的NumPy转换
        try:
            predictions = predictions_tensor.detach().numpy()
        except RuntimeError:
            # 如果numpy()失败，使用替代方法
            predictions = predictions_tensor.detach().cpu().numpy()
        except:
            # 最后的备用方法
            predictions_list = predictions_tensor.detach().tolist()
            predictions = np.array(predictions_list)

    # 计算评估指标
    mse = mean_squared_error(y_test, predictions)
    r2 = r2_score(y_test, predictions)
    
    # 分通道评估
    channel_names = ['R', 'G', 'B']
    print(f"\n=== 模型评估结果 ===")
    print(f"整体 MSE: {mse:.4f}")
    print(f"整体 R²: {r2:.4f}")
    
    for i, channel in enumerate(channel_names):
        channel_mse = mean_squared_error(y_test[:, i], predictions[:, i])
        channel_r2 = r2_score(y_test[:, i], predictions[:, i])
        print(f"{channel}通道 MSE: {channel_mse:.4f}, R²: {channel_r2:.4f}")
    
    return predictions

def plot_results(train_losses, val_losses, y_test, predictions):
    """绘制结果"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # 训练损失曲线
    axes[0, 0].plot(train_losses, label='训练损失', alpha=0.7)
    axes[0, 0].plot(val_losses, label='验证损失', alpha=0.7)
    axes[0, 0].set_xlabel('轮次')
    axes[0, 0].set_ylabel('损失')
    axes[0, 0].set_title('训练过程')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # RGB通道预测对比
    channel_names = ['R', 'G', 'B']
    colors = ['red', 'green', 'blue']
    
    for i, (channel, color) in enumerate(zip(channel_names, colors)):
        row = (i + 1) // 2
        col = (i + 1) % 2
        
        axes[row, col].scatter(y_test[:, i], predictions[:, i], alpha=0.6, color=color, s=50)
        axes[row, col].plot([y_test[:, i].min(), y_test[:, i].max()], 
                           [y_test[:, i].min(), y_test[:, i].max()], 'k--', alpha=0.8)
        axes[row, col].set_xlabel(f'真实{channel}值')
        axes[row, col].set_ylabel(f'预测{channel}值')
        axes[row, col].set_title(f'{channel}通道预测效果')
        axes[row, col].grid(True, alpha=0.3)
        
        # 计算R²
        r2 = r2_score(y_test[:, i], predictions[:, i])
        axes[row, col].text(0.05, 0.95, f'R² = {r2:.4f}', 
                           transform=axes[row, col].transAxes, 
                           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('双输入RGB神经网络预测结果_2025-07-23.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("=== 双输入RGB神经网络预测系统 ===")
    
    # 准备数据
    X, y = prepare_data()
    print(f"数据形状: X={X.shape}, y={y.shape}")
    
    # 数据标准化
    from sklearn.preprocessing import StandardScaler
    scaler_X = StandardScaler()
    scaler_y = StandardScaler()
    
    X_scaled = scaler_X.fit_transform(X)
    y_scaled = scaler_y.fit_transform(y)
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y_scaled, test_size=0.2, random_state=42
    )
    
    # 创建数据加载器
    train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train))
    test_dataset = TensorDataset(torch.FloatTensor(X_test), torch.FloatTensor(y_test))
    
    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)
    val_loader = DataLoader(test_dataset, batch_size=16, shuffle=False)
    
    # 创建模型
    model = DualInputRGBNet(
        input_dim=6,
        hidden_dims=[128, 64, 32, 16],
        output_dim=3,
        dropout_rate=0.3
    )
    
    print(f"模型结构:\n{model}")
    
    # 训练模型
    train_losses, val_losses = train_model(
        model, train_loader, val_loader,
        num_epochs=2000,
        learning_rate=0.001,
        weight_decay=1e-4
    )
    
    # 评估模型
    predictions_scaled = evaluate_model(model, X_test, y_test)
    
    # 反标准化
    predictions = scaler_y.inverse_transform(predictions_scaled)
    y_test_original = scaler_y.inverse_transform(y_test)
    
    # 绘制结果
    plot_results(train_losses, val_losses, y_test_original, predictions)
    
    print("\n=== 训练完成 ===")
    print("最佳模型已保存为: best_rgb_model.pth")
    print("结果图表已保存为: 双输入RGB神经网络预测结果_2025-07-23.png")

if __name__ == "__main__":
    main()
