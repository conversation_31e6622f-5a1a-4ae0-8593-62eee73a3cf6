# Copyright (c) 2025 左岚. All rights reserved.

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代简约简历模板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .resume-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }
        
        .profile-img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            margin: 0 auto 20px;
            object-fit: cover;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }
        
        .name {
            font-size: 2.5em;
            font-weight: 300;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .title {
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
            position: relative;
            z-index: 1;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 0;
        }
        
        .sidebar {
            background: #f8f9fa;
            padding: 40px 30px;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 35px;
        }
        
        .section-title {
            font-size: 1.4em;
            color: #4facfe;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid #4facfe;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30px;
            height: 2px;
            background: #00f2fe;
        }
        
        .skill-category {
            margin-bottom: 25px;
        }
        
        .skill-category h4 {
            color: #555;
            margin-bottom: 12px;
            font-size: 1.1em;
        }
        
        .skill-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .skill-tag {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
        }
        
        .education-item {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
            border-left: 4px solid #4facfe;
        }
        
        .education-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .degree {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
        }
        
        .university {
            color: #666;
            font-size: 1em;
            margin-top: 5px;
        }
        
        .date {
            background: #4facfe;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            white-space: nowrap;
        }
        
        .project-highlights {
            margin-top: 15px;
        }
        
        .project-highlights h5 {
            color: #4facfe;
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .highlight-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            padding-left: 15px;
            position: relative;
        }
        
        .highlight-item::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #4facfe;
            font-size: 0.8em;
        }
        
        .highlight-item span {
            font-size: 0.9em;
            color: #555;
            line-height: 1.5;
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 10px;
        }
        
        .tech-item {
            background: #e3f2fd;
            color: #1976d2;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75em;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .contact-info {
                flex-direction: column;
                gap: 10px;
            }
            
            .education-header {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <!-- 头部区域 -->
        <div class="header">
            <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 120 120'><rect width='120' height='120' fill='%234facfe'/><text x='60' y='70' text-anchor='middle' fill='white' font-size='40' font-family='Arial'>头像</text></svg>" alt="个人照片" class="profile-img">
            <h1 class="name">程泽</h1>
            <p class="title">电子信息工程师 | 图像处理专家</p>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📧</span>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <span>📱</span>
                    <span>138-0000-0000</span>
                </div>
                <div class="contact-item">
                    <span>📍</span>
                    <span>广州</span>
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 核心技能 -->
                <div class="section">
                    <h3 class="section-title">核心技能</h3>
                    
                    <div class="skill-category">
                        <h4>编程语言</h4>
                        <div class="skill-tags">
                            <span class="skill-tag">Python</span>
                            <span class="skill-tag">C++</span>
                            <span class="skill-tag">MATLAB</span>
                            <span class="skill-tag">JavaScript</span>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h4>图像处理</h4>
                        <div class="skill-tags">
                            <span class="skill-tag">OpenCV</span>
                            <span class="skill-tag">颜色校正</span>
                            <span class="skill-tag">白平衡算法</span>
                            <span class="skill-tag">LAB色彩空间</span>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h4>机器学习</h4>
                        <div class="skill-tags">
                            <span class="skill-tag">深度学习</span>
                            <span class="skill-tag">PyTorch</span>
                            <span class="skill-tag">TensorFlow</span>
                            <span class="skill-tag">神经网络</span>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h4>专业工具</h4>
                        <div class="skill-tags">
                            <span class="skill-tag">Git</span>
                            <span class="skill-tag">Docker</span>
                            <span class="skill-tag">Linux</span>
                            <span class="skill-tag">数据分析</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要内容 -->
            <div class="content">
                <!-- 教育背景 -->
                <div class="section">
                    <h3 class="section-title">教育背景</h3>
                    
                    <div class="education-item">
                        <div class="education-header">
                            <div>
                                <div class="degree">电子信息工程 学士学位</div>
                                <div class="university">华南师范大学</div>
                            </div>
                            <div class="date">2024年毕业</div>
                        </div>
                        
                        <div class="project-highlights">
                            <h5>核心项目经验</h5>
                            <div class="highlight-item">
                                <span>开发了基于深度学习的颜色校正系统，实现了医疗试纸的精确颜色检测</span>
                            </div>
                            <div class="highlight-item">
                                <span>设计并实现了三阶根特征增强RGB神经网络，提升颜色预测精度15%</span>
                            </div>
                            <div class="highlight-item">
                                <span>掌握Gray World、White Patch等经典白平衡算法及其工程实现</span>
                            </div>
                            <div class="highlight-item">
                                <span>熟练运用LAB色彩空间进行颜色分析和校正，具备完整的图像处理流程经验</span>
                            </div>
                            
                            <div class="tech-stack">
                                <span class="tech-item">Python</span>
                                <span class="tech-item">OpenCV</span>
                                <span class="tech-item">PyTorch</span>
                                <span class="tech-item">颜色校正</span>
                                <span class="tech-item">深度学习</span>
                                <span class="tech-item">图像处理</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目经验 -->
                <div class="section">
                    <h3 class="section-title">核心项目</h3>
                    
                    <div class="education-item">
                        <div class="education-header">
                            <div>
                                <div class="degree">医疗试纸颜色检测系统</div>
                                <div class="university">基于深度学习的智能颜色校正</div>
                            </div>
                            <div class="date">2024</div>
                        </div>
                        
                        <div class="project-highlights">
                            <h5>技术亮点</h5>
                            <div class="highlight-item">
                                <span>实现了环境光自适应补偿算法，支持不同光照条件下的准确检测</span>
                            </div>
                            <div class="highlight-item">
                                <span>开发了三阶根特征工程方法，增强RGB通道间的内部联系</span>
                            </div>
                            <div class="highlight-item">
                                <span>构建了多分支神经网络架构，实现像素级和色块级双重校正策略</span>
                            </div>
                            
                            <div class="tech-stack">
                                <span class="tech-item">Python</span>
                                <span class="tech-item">OpenCV</span>
                                <span class="tech-item">PyTorch</span>
                                <span class="tech-item">LAB色彩空间</span>
                                <span class="tech-item">白平衡算法</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="education-item">
                        <div class="education-header">
                            <div>
                                <div class="degree">72色比色卡颜色校正算法</div>
                                <div class="university">精密颜色校正与评估系统</div>
                            </div>
                            <div class="date">2024</div>
                        </div>
                        
                        <div class="project-highlights">
                            <h5>核心成果</h5>
                            <div class="highlight-item">
                                <span>实现了多模型融合的颜色校正算法，包括线性回归、高斯过程等</span>
                            </div>
                            <div class="highlight-item">
                                <span>建立了完整的颜色质量评估体系，结合技术指标和视觉感受</span>
                            </div>
                            <div class="highlight-item">
                                <span>开发了自适应强度控制机制，避免过度校正问题</span>
                            </div>
                            
                            <div class="tech-stack">
                                <span class="tech-item">机器学习</span>
                                <span class="tech-item">颜色科学</span>
                                <span class="tech-item">数据分析</span>
                                <span class="tech-item">算法优化</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
