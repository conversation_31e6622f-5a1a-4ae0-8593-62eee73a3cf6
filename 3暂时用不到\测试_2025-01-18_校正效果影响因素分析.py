import numpy as np
import matplotlib.pyplot as plt
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
def analyze_correction_factors():
    """分析影响校正效果的多重因素"""
    print("=== 影响颜色校正效果的多重因素分析 ===\n")
    
    # 定义不同G值区域
    regions = {
        "低值区域 (≤100)": {"g_range": (70, 100), "count": 19},
        "中低值区域 (101-150)": {"g_range": (101, 150), "count": 24},
        "中高值区域 (151-200)": {"g_range": (151, 200), "count": 20},
        "高值区域 (>200)": {"g_range": (201, 231), "count": 9}
    }
    
    print("1. 【色卡分布因素】")
    for region, info in regions.items():
        density = info["count"] / (info["g_range"][1] - info["g_range"][0])
        print(f"   {region}: {info['count']}个色块, 密度={density:.2f}个/G值")
    
    print("\n2. 【信噪比因素】")
    for region, info in regions.items():
        g_mid = (info["g_range"][0] + info["g_range"][1]) / 2
        # 假设噪声标准差为固定值3
        noise_std = 3
        snr = g_mid / noise_std
        print(f"   {region}: 信噪比≈{snr:.1f}:1")
    
    print("\n3. 【曝光/量化因素】")
    print("   低值区域: 量化级别有限，暗部细节丢失")
    print("   中低值区域: 量化充分，细节保留好")
    print("   中高值区域: 量化充分，动态范围好")
    print("   高值区域: 容易过曝，高光溢出")
    
    print("\n4. 【线性度因素】")
    print("   低值区域: 非线性响应明显，gamma校正影响大")
    print("   中等区域: 接近线性响应，校正模型简单有效")
    print("   高值区域: 可能进入饱和区，非线性增强")

def simulate_noise_impact():
    """模拟噪声对不同亮度区域的影响"""
    print("\n=== 噪声影响仿真 ===")
    
    # 不同亮度的信号值
    signal_levels = [80, 125, 175, 215]  # 对应四个区域的典型G值
    region_names = ["低值区域", "中低值区域", "中高值区域", "高值区域"]
    
    # 固定噪声标准差
    noise_std = 3
    
    print("假设噪声标准差=3的情况下：")
    print("区域\t\t信号值\t信噪比\t相对误差%\t校正难度")
    print("-" * 60)
    
    for i, (signal, name) in enumerate(zip(signal_levels, region_names)):
        snr = signal / noise_std
        relative_error = (noise_std / signal) * 100
        
        if relative_error < 2:
            difficulty = "容易"
        elif relative_error < 4:
            difficulty = "适中"
        elif relative_error < 6:
            difficulty = "困难"
        else:
            difficulty = "很困难"
            
        print(f"{name}\t{signal}\t{snr:.1f}\t{relative_error:.1f}%\t\t{difficulty}")

def analyze_quantization_effects():
    """分析量化效应的影响"""
    print("\n=== 量化效应分析 ===")
    
    print("8位图像(0-255)的量化问题：")
    print("\n1. 低值区域 (70-100):")
    print("   - 只有30个量化级别")
    print("   - 每级代表约3.3%的相对变化")
    print("   - 量化噪声影响大")
    
    print("\n2. 中等区域 (101-200):")
    print("   - 有99个量化级别")
    print("   - 每级代表约1%的相对变化")
    print("   - 量化精度充足")
    
    print("\n3. 高值区域 (201-255):")
    print("   - 有54个量化级别")
    print("   - 容易饱和到255")
    print("   - 高光细节丢失")

def camera_response_analysis():
    """分析相机响应特性"""
    print("\n=== 相机响应特性分析 ===")
    
    print("典型相机传感器特性：")
    print("\n1. 暗电流噪声:")
    print("   - 在暗部(低G值)影响最大")
    print("   - 造成暗部信噪比下降")
    
    print("\n2. 满阱容量:")
    print("   - 高亮度时容易饱和")
    print("   - 导致高G值区域非线性")
    
    print("\n3. Gamma校正:")
    print("   - 对暗部进行非线性提升")
    print("   - 改变了原始线性关系")
    
    print("\n4. 自动曝光:")
    print("   - 可能导致高光过曝")
    print("   - 影响高G值区域的准确性")

def environmental_factors():
    """分析环境因素的影响"""
    print("\n=== 环境因素影响 ===")
    
    print("1. 光照不均匀:")
    print("   - 对高G值(亮色)影响更明显")
    print("   - 造成色温偏移")
    
    print("\n2. 反射和散射:")
    print("   - 在暗部(低G值)造成杂散光")
    print("   - 提升黑电平，压缩动态范围")
    
    print("\n3. 色温变化:")
    print("   - 对白色(高G值)影响最大")
    print("   - 需要白平衡校正")

def comprehensive_solution():
    """综合解决方案"""
    print("\n=== 针对性解决方案 ===")
    
    print("🔴 低值区域 (≤100) 优化策略:")
    print("   1. 增加曝光时间，提升信号强度")
    print("   2. 使用降噪算法减少噪声")
    print("   3. 采用非线性校正模型")
    print("   4. 增加该区域的色卡密度")
    
    print("\n🟡 中等区域 (101-200) 保持策略:")
    print("   1. 维持当前色卡分布")
    print("   2. 使用线性校正模型")
    print("   3. 这是最佳校正区域")
    
    print("\n🟠 高值区域 (>200) 优化策略:")
    print("   1. 控制曝光，避免过曝")
    print("   2. 使用HDR技术扩展动态范围")
    print("   3. 增加白平衡参考点")
    print("   4. 考虑分段校正")

def create_visualization():
    """创建可视化图表"""
    plt.figure(figsize=(13, 8))
    
    # 子图1: 信噪比对比
    plt.subplot(2, 3, 1)
    regions = ["低值", "中低值", "中高值", "高值"]
    snr_values = [26.7, 41.7, 58.3, 71.7]
    colors = ['red', 'orange', 'lightgreen', 'green']
    bars = plt.bar(regions, snr_values, color=colors, alpha=0.7)
    plt.ylabel('信噪比')
    plt.title('不同区域信噪比对比')
    plt.xticks(rotation=45)
    for bar, val in zip(bars, snr_values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{val:.1f}', ha='center', va='bottom')
    
    # 子图2: 相对误差对比
    plt.subplot(2, 3, 2)
    relative_errors = [3.75, 2.4, 1.71, 1.4]
    bars = plt.bar(regions, relative_errors, color=colors, alpha=0.7)
    plt.ylabel('相对误差 (%)')
    plt.title('噪声相对误差对比')
    plt.xticks(rotation=45)
    for bar, val in zip(bars, relative_errors):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                f'{val:.1f}%', ha='center', va='bottom')
    
    # 子图3: 量化级别对比
    plt.subplot(2, 3, 3)
    quantization_levels = [30, 49, 49, 54]
    bars = plt.bar(regions, quantization_levels, color=colors, alpha=0.7)
    plt.ylabel('量化级别数')
    plt.title('可用量化级别对比')
    plt.xticks(rotation=45)
    for bar, val in zip(bars, quantization_levels):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{val}', ha='center', va='bottom')
    
    # 子图4: 色卡密度对比
    plt.subplot(2, 3, 4)
    densities = [0.63, 0.49, 0.41, 0.30]
    bars = plt.bar(regions, densities, color=colors, alpha=0.7)
    plt.ylabel('色卡密度 (个/G值)')
    plt.title('色卡分布密度对比')
    plt.xticks(rotation=45)
    for bar, val in zip(bars, densities):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{val:.2f}', ha='center', va='bottom')
    
    # 子图5: 综合校正难度评分
    plt.subplot(2, 3, 5)
    # 综合评分：信噪比(40%) + 量化(20%) + 色卡密度(20%) + 线性度(20%)
    snr_scores = [26.7/71.7*40, 41.7/71.7*40, 58.3/71.7*40, 71.7/71.7*40]
    quant_scores = [30/54*20, 49/54*20, 49/54*20, 54/54*20]
    density_scores = [0.63/0.63*20, 0.49/0.63*20, 0.41/0.63*20, 0.30/0.63*20]
    linear_scores = [10, 18, 20, 15]  # 主观评分
    
    total_scores = [s+q+d+l for s,q,d,l in zip(snr_scores, quant_scores, density_scores, linear_scores)]
    
    bars = plt.bar(regions, total_scores, color=colors, alpha=0.7)
    plt.ylabel('综合校正效果评分')
    plt.title('综合校正效果评估')
    plt.xticks(rotation=45)
    for bar, val in zip(bars, total_scores):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                f'{val:.0f}', ha='center', va='bottom')
    
    # 子图6: 影响因素权重分析
    plt.subplot(2, 3, 6)
    factors = ['信噪比', '量化精度', '色卡密度', '线性度', '环境因素']
    weights = [40, 20, 20, 15, 5]
    plt.pie(weights, labels=factors, autopct='%1.1f%%', startangle=90)
    plt.title('影响因素权重分析')
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    analyze_correction_factors()
    simulate_noise_impact()
    analyze_quantization_effects()
    camera_response_analysis()
    environmental_factors()
    comprehensive_solution()
    create_visualization()
    
    print("\n=== 总结 ===")
    print("高值和低值区域校正效果不好的原因是多重因素叠加：")
    print("1. 色卡分布密度 (20%权重)")
    print("2. 信噪比问题 (40%权重) ← 最重要因素")
    print("3. 量化精度 (20%权重)")
    print("4. 相机响应线性度 (15%权重)")
    print("5. 环境光照因素 (5%权重)")
    print("\n其中信噪比是最关键的影响因素！")
